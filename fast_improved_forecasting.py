"""
FAST IMPROVED Electric Demand Forecasting
Key improvements without extensive parameter search for quick results
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler

plt.style.use('seaborn-v0_8')

class FastImprovedForecaster:
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.results = {}
        self.scaler = StandardScaler()
        
    def load_and_prepare_data(self):
        """Fast data preparation with key improvements"""
        print("🚀 FAST IMPROVED FORECASTING - Loading data...")
        
        # Load and prepare data
        self.data = pd.read_csv(self.data_path)
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        self.data = self.data.asfreq('H')  # Set hourly frequency
        
        print(f"Data: {len(self.data)} samples from {self.data.index.min()} to {self.data.index.max()}")
        
        # Enhanced feature engineering
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data.index.hour / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data.index.hour / 24)
        self.data['day_sin'] = np.sin(2 * np.pi * self.data.index.dayofweek / 7)
        self.data['day_cos'] = np.cos(2 * np.pi * self.data.index.dayofweek / 7)
        
        # Scale temperature
        temp_scaled = self.scaler.fit_transform(self.data[['Temperature']])
        self.data['Temperature_scaled'] = temp_scaled.flatten()
        
        # Exogenous variables
        self.exog_cols = ['Temperature_scaled', 'Holiday', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos']
        self.exog_data = self.data[self.exog_cols].copy()
        
        return self.data
    
    def split_data(self):
        """Split data chronologically"""
        total_len = len(self.data)
        train_end = int(0.7 * total_len)
        test_end = int(0.9 * total_len)
        
        self.train_data = self.data.iloc[:train_end].copy()
        self.test_data = self.data.iloc[train_end:test_end].copy()
        self.validation_data = self.data.iloc[test_end:].copy()
        
        self.train_exog = self.exog_data.iloc[:train_end].copy()
        self.test_exog = self.exog_data.iloc[train_end:test_end].copy()
        self.validation_exog = self.exog_data.iloc[test_end:].copy()
        
        print(f"Split: Train={len(self.train_data)}, Test={len(self.test_data)}, Val={len(self.validation_data)}")
        
    def train_improved_models(self):
        """Train improved models with good default parameters"""
        print("\n🔧 Training improved models...")
        
        # 1. Improved SARIMAX with good parameters for hourly data
        print("Training SARIMAX...")
        try:
            self.sarimax_model = SARIMAX(
                self.train_data['Demand'],
                exog=self.train_exog,
                order=(1, 1, 1),           # Simple ARIMA
                seasonal_order=(1, 1, 1, 24),  # Daily seasonality
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            self.sarimax_fitted = self.sarimax_model.fit(disp=False, maxiter=100)
            print(f"✓ SARIMAX trained - AIC: {self.sarimax_fitted.aic:.2f}")
            
        except Exception as e:
            print(f"SARIMAX failed: {e}, using ARIMA fallback")
            self.sarimax_model = ARIMA(self.train_data['Demand'], order=(2, 1, 2))
            self.sarimax_fitted = self.sarimax_model.fit()
        
        # 2. Improved Holt-Winter with optimization
        print("Training Holt-Winter...")
        try:
            self.holt_winter_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24,
                initialization_method='estimated'
            )
            self.holt_winter_fitted = self.holt_winter_model.fit(
                optimized=True,
                use_brute=False,  # Faster optimization
                remove_bias=True
            )
            print(f"✓ Holt-Winter trained - AIC: {self.holt_winter_fitted.aic:.2f}")
            
        except Exception as e:
            print(f"Optimized Holt-Winter failed: {e}, using simple version")
            self.holt_winter_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24
            )
            self.holt_winter_fitted = self.holt_winter_model.fit()
    
    def evaluate_models(self):
        """Evaluate models on test and validation sets"""
        print("\n📊 Evaluating models...")
        
        # Test set evaluation
        test_steps = len(self.test_data)
        
        # SARIMAX forecast
        try:
            if hasattr(self.sarimax_fitted, 'get_forecast'):
                forecast_result = self.sarimax_fitted.get_forecast(
                    steps=test_steps,
                    exog=self.test_exog
                )
                sarimax_test_forecast = forecast_result.predicted_mean
            else:
                sarimax_test_forecast = self.sarimax_fitted.forecast(steps=test_steps)
            sarimax_test_forecast.index = self.test_data.index
            
        except Exception as e:
            print(f"SARIMAX test forecast error: {e}")
            sarimax_test_forecast = pd.Series([self.train_data['Demand'].mean()] * test_steps,
                                            index=self.test_data.index)
        
        # Holt-Winter forecast
        try:
            hw_test_forecast = self.holt_winter_fitted.forecast(steps=test_steps)
            hw_test_forecast.index = self.test_data.index
        except Exception as e:
            print(f"Holt-Winter test forecast error: {e}")
            hw_test_forecast = pd.Series([self.train_data['Demand'].mean()] * test_steps,
                                       index=self.test_data.index)
        
        # Calculate test metrics
        actual_test = self.test_data['Demand']
        
        def calc_metrics(actual, predicted):
            mae = mean_absolute_error(actual, predicted)
            rmse = np.sqrt(mean_squared_error(actual, predicted))
            mape = np.mean(np.abs((actual - predicted) / actual)) * 100
            return {'MAE': mae, 'RMSE': rmse, 'MAPE': mape}
        
        sarimax_test_metrics = calc_metrics(actual_test, sarimax_test_forecast)
        hw_test_metrics = calc_metrics(actual_test, hw_test_forecast)
        
        print("\n📈 Test Set Results:")
        print(f"SARIMAX: MAE={sarimax_test_metrics['MAE']:.2f}, MAPE={sarimax_test_metrics['MAPE']:.2f}%")
        print(f"Holt-Winter: MAE={hw_test_metrics['MAE']:.2f}, MAPE={hw_test_metrics['MAPE']:.2f}%")
        
        # Retrain on combined data for validation
        print("\n🔄 Retraining on combined data...")
        combined_data = pd.concat([self.train_data, self.test_data])
        combined_exog = pd.concat([self.train_exog, self.test_exog])
        
        # Retrain SARIMAX
        try:
            if hasattr(self.sarimax_fitted, 'model'):
                self.sarimax_final = SARIMAX(
                    combined_data['Demand'],
                    exog=combined_exog,
                    order=(1, 1, 1),
                    seasonal_order=(1, 1, 1, 24),
                    enforce_stationarity=False,
                    enforce_invertibility=False
                )
                self.sarimax_final_fitted = self.sarimax_final.fit(disp=False, maxiter=50)
            else:
                self.sarimax_final_fitted = self.sarimax_fitted
        except:
            self.sarimax_final_fitted = self.sarimax_fitted
        
        # Retrain Holt-Winter
        try:
            self.holt_winter_final = ExponentialSmoothing(
                combined_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24
            )
            self.holt_winter_final_fitted = self.holt_winter_final.fit(optimized=True)
        except:
            self.holt_winter_final_fitted = self.holt_winter_fitted
        
        # Validation forecasts
        val_steps = len(self.validation_data)
        horizons = {
            '24h': min(24, val_steps),
            '168h': min(168, val_steps),
            'full': val_steps
        }
        
        validation_results = {}
        
        for horizon_name, steps in horizons.items():
            print(f"\n🎯 Validating {horizon_name} ({steps} steps)...")
            
            # SARIMAX validation forecast
            try:
                if hasattr(self.sarimax_final_fitted, 'get_forecast'):
                    val_forecast_result = self.sarimax_final_fitted.get_forecast(
                        steps=steps,
                        exog=self.validation_exog.iloc[:steps]
                    )
                    sarimax_val_forecast = val_forecast_result.predicted_mean
                else:
                    sarimax_val_forecast = self.sarimax_final_fitted.forecast(steps=steps)
                sarimax_val_forecast.index = self.validation_data.index[:steps]
            except Exception as e:
                print(f"SARIMAX validation error: {e}")
                sarimax_val_forecast = pd.Series([combined_data['Demand'].mean()] * steps,
                                               index=self.validation_data.index[:steps])
            
            # Holt-Winter validation forecast
            try:
                hw_val_forecast = self.holt_winter_final_fitted.forecast(steps=steps)
                hw_val_forecast.index = self.validation_data.index[:steps]
            except Exception as e:
                print(f"Holt-Winter validation error: {e}")
                hw_val_forecast = pd.Series([combined_data['Demand'].mean()] * steps,
                                          index=self.validation_data.index[:steps])
            
            # Calculate validation metrics
            actual_val = self.validation_data['Demand'].iloc[:steps]
            sarimax_val_metrics = calc_metrics(actual_val, sarimax_val_forecast)
            hw_val_metrics = calc_metrics(actual_val, hw_val_forecast)
            
            validation_results[horizon_name] = {
                'SARIMAX': sarimax_val_metrics,
                'Holt-Winter': hw_val_metrics,
                'forecasts': {
                    'actual': actual_val,
                    'sarimax': sarimax_val_forecast,
                    'holt_winter': hw_val_forecast
                }
            }
            
            print(f"  SARIMAX: MAE={sarimax_val_metrics['MAE']:.2f}, MAPE={sarimax_val_metrics['MAPE']:.2f}%")
            print(f"  Holt-Winter: MAE={hw_val_metrics['MAE']:.2f}, MAPE={hw_val_metrics['MAPE']:.2f}%")
        
        self.results = {
            'test_metrics': {'SARIMAX': sarimax_test_metrics, 'Holt-Winter': hw_test_metrics},
            'validation_metrics': validation_results
        }
        
        return validation_results

    def create_improved_visualizations(self):
        """Create improved comparison visualizations"""
        print("\n🎨 Creating improved visualizations...")

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # 24h forecast comparison
        if '24h' in self.results['validation_metrics']:
            data_24h = self.results['validation_metrics']['24h']['forecasts']
            axes[0,0].plot(data_24h['actual'].index, data_24h['actual'].values,
                          'k-', label='Actual', linewidth=2)
            axes[0,0].plot(data_24h['sarimax'].index, data_24h['sarimax'].values,
                          'b-', label='SARIMAX', alpha=0.8, linewidth=1.5)
            axes[0,0].plot(data_24h['holt_winter'].index, data_24h['holt_winter'].values,
                          'r-', label='Holt-Winter', alpha=0.8, linewidth=1.5)
            axes[0,0].set_title('24-Hour Forecast vs Actual (Improved)')
            axes[0,0].legend()
            axes[0,0].grid(True, alpha=0.3)
            axes[0,0].tick_params(axis='x', rotation=45)

        # 168h forecast comparison
        if '168h' in self.results['validation_metrics']:
            data_168h = self.results['validation_metrics']['168h']['forecasts']
            axes[0,1].plot(data_168h['actual'].index, data_168h['actual'].values,
                          'k-', label='Actual', linewidth=2)
            axes[0,1].plot(data_168h['sarimax'].index, data_168h['sarimax'].values,
                          'b-', label='SARIMAX', alpha=0.8, linewidth=1.5)
            axes[0,1].plot(data_168h['holt_winter'].index, data_168h['holt_winter'].values,
                          'r-', label='Holt-Winter', alpha=0.8, linewidth=1.5)
            axes[0,1].set_title('168-Hour (1 Week) Forecast vs Actual (Improved)')
            axes[0,1].legend()
            axes[0,1].grid(True, alpha=0.3)
            axes[0,1].tick_params(axis='x', rotation=45)

        # Performance comparison - MAE
        horizons = list(self.results['validation_metrics'].keys())
        sarimax_maes = [self.results['validation_metrics'][h]['SARIMAX']['MAE'] for h in horizons]
        hw_maes = [self.results['validation_metrics'][h]['Holt-Winter']['MAE'] for h in horizons]

        x = np.arange(len(horizons))
        width = 0.35

        bars1 = axes[1,0].bar(x - width/2, sarimax_maes, width, label='SARIMAX', alpha=0.8, color='blue')
        bars2 = axes[1,0].bar(x + width/2, hw_maes, width, label='Holt-Winter', alpha=0.8, color='red')

        # Add value labels on bars
        for bar in bars1:
            height = bar.get_height()
            axes[1,0].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{height:.0f}', ha='center', va='bottom', fontsize=10)
        for bar in bars2:
            height = bar.get_height()
            axes[1,0].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{height:.0f}', ha='center', va='bottom', fontsize=10)

        axes[1,0].set_xlabel('Forecast Horizon')
        axes[1,0].set_ylabel('Mean Absolute Error (MAE)')
        axes[1,0].set_title('MAE Comparison - Improved Models')
        axes[1,0].set_xticks(x)
        axes[1,0].set_xticklabels(horizons)
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)

        # Performance comparison - MAPE
        sarimax_mapes = [self.results['validation_metrics'][h]['SARIMAX']['MAPE'] for h in horizons]
        hw_mapes = [self.results['validation_metrics'][h]['Holt-Winter']['MAPE'] for h in horizons]

        bars3 = axes[1,1].bar(x - width/2, sarimax_mapes, width, label='SARIMAX', alpha=0.8, color='blue')
        bars4 = axes[1,1].bar(x + width/2, hw_mapes, width, label='Holt-Winter', alpha=0.8, color='red')

        # Add value labels on bars
        for bar in bars3:
            height = bar.get_height()
            axes[1,1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{height:.1f}%', ha='center', va='bottom', fontsize=10)
        for bar in bars4:
            height = bar.get_height()
            axes[1,1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{height:.1f}%', ha='center', va='bottom', fontsize=10)

        axes[1,1].set_xlabel('Forecast Horizon')
        axes[1,1].set_ylabel('Mean Absolute Percentage Error (MAPE %)')
        axes[1,1].set_title('MAPE Comparison - Improved Models')
        axes[1,1].set_xticks(x)
        axes[1,1].set_xticklabels(horizons)
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('fast_improved_forecasting_results.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Print final conclusions
        print("\n" + "="*60)
        print("🏆 IMPROVED FORECASTING CONCLUSIONS")
        print("="*60)

        # Determine best model for each horizon
        for horizon in horizons:
            sarimax_mae = self.results['validation_metrics'][horizon]['SARIMAX']['MAE']
            hw_mae = self.results['validation_metrics'][horizon]['Holt-Winter']['MAE']

            best_model = 'SARIMAX' if sarimax_mae < hw_mae else 'Holt-Winter'
            improvement = abs(sarimax_mae - hw_mae) / max(sarimax_mae, hw_mae) * 100

            print(f"\n{horizon} Forecast:")
            print(f"  🥇 Best Model: {best_model}")
            print(f"  📈 Improvement: {improvement:.1f}%")
            print(f"  📊 SARIMAX MAE: {sarimax_mae:.2f}")
            print(f"  📊 Holt-Winter MAE: {hw_mae:.2f}")

        # Overall recommendation
        avg_sarimax_mae = np.mean([self.results['validation_metrics'][h]['SARIMAX']['MAE'] for h in horizons])
        avg_hw_mae = np.mean([self.results['validation_metrics'][h]['Holt-Winter']['MAE'] for h in horizons])

        overall_best = 'SARIMAX' if avg_sarimax_mae < avg_hw_mae else 'Holt-Winter'
        overall_improvement = abs(avg_sarimax_mae - avg_hw_mae) / max(avg_sarimax_mae, avg_hw_mae) * 100

        print(f"\n{'='*60}")
        print(f"🎯 OVERALL RECOMMENDATION: {overall_best}")
        print(f"📊 Average MAE - SARIMAX: {avg_sarimax_mae:.2f}")
        print(f"📊 Average MAE - Holt-Winter: {avg_hw_mae:.2f}")
        print(f"🚀 Overall Improvement: {overall_improvement:.1f}%")
        print(f"{'='*60}")

        return overall_best

    def run_fast_improved_analysis(self):
        """Run the complete fast improved analysis"""
        print("⚡ FAST IMPROVED ELECTRIC DEMAND FORECASTING")
        print("Enhanced methodology with optimized performance")
        print("="*60)

        try:
            self.load_and_prepare_data()
            self.split_data()
            self.train_improved_models()
            self.evaluate_models()
            best_model = self.create_improved_visualizations()

            print(f"\n✅ Fast improved analysis completed!")
            print(f"🏆 Recommended model: {best_model}")

            return self.results, best_model

        except Exception as e:
            print(f"❌ Error: {e}")
            import traceback
            traceback.print_exc()
            return None, None


def main():
    """Main execution for fast improved forecasting"""
    forecaster = FastImprovedForecaster('electric_demand_1h.csv')
    results, best_model = forecaster.run_fast_improved_analysis()

    if results:
        print(f"\n🎉 SUCCESS! Improved forecasting completed.")
        print(f"📁 Results saved as 'fast_improved_forecasting_results.png'")
    else:
        print("❌ Analysis failed.")


if __name__ == "__main__":
    main()
