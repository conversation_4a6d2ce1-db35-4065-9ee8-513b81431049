"""
Electric Demand Regression Analysis
Complete regression modeling with 4 algorithms: XGBoost, Random Forest, LightGBM, Linear Regression

Features:
- Cross-validation training and testing
- Hyperparameter tuning for all models
- Best model selection and visualization
- Comprehensive performance comparison
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Machine Learning Libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, TimeSeriesSplit
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import lightgbm as lgb

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class RegressionAnalysis:
    def __init__(self, data_path):
        """Initialize the regression analyzer"""
        self.data_path = data_path
        self.data = None
        self.X = None
        self.y = None
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.models = {}
        self.best_models = {}
        self.results = {}
        self.scaler = StandardScaler()
        self.best_model_name = None
        self.best_model = None
        
    def load_and_prepare_data(self):
        """Load and prepare data with comprehensive feature engineering"""
        print("🤖 ELECTRIC DEMAND REGRESSION ANALYSIS")
        print("="*60)
        print("📊 Loading and preparing data...")
        
        # Load data
        self.data = pd.read_csv(self.data_path)
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        
        print(f"Data shape: {self.data.shape}")
        print(f"Date range: {self.data.index.min()} to {self.data.index.max()}")
        
        # Comprehensive feature engineering
        print("🔧 Engineering features...")
        
        # Time-based features
        self.data['hour'] = self.data.index.hour
        self.data['day_of_week'] = self.data.index.dayofweek
        self.data['month'] = self.data.index.month
        self.data['day_of_year'] = self.data.index.dayofyear
        
        # Cyclical features (important for time patterns)
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data['hour'] / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data['hour'] / 24)
        self.data['day_sin'] = np.sin(2 * np.pi * self.data['day_of_week'] / 7)
        self.data['day_cos'] = np.cos(2 * np.pi * self.data['day_of_week'] / 7)
        self.data['month_sin'] = np.sin(2 * np.pi * self.data['month'] / 12)
        self.data['month_cos'] = np.cos(2 * np.pi * self.data['month'] / 12)
        
        # Lag features (crucial for time series)
        for lag in [1, 24, 168]:  # 1h, 1day, 1week lags
            self.data[f'demand_lag_{lag}'] = self.data['Demand'].shift(lag)
        
        # Rolling statistics
        for window in [24, 168]:  # 1day, 1week windows
            self.data[f'demand_rolling_mean_{window}'] = self.data['Demand'].rolling(window=window).mean()
            self.data[f'demand_rolling_std_{window}'] = self.data['Demand'].rolling(window=window).std()
        
        # Temperature features
        self.data['temp_squared'] = self.data['Temperature'] ** 2
        self.data['temp_rolling_mean_24'] = self.data['Temperature'].rolling(window=24).mean()
        
        # Select features for modeling
        feature_cols = [
            'Temperature', 'temp_squared', 'temp_rolling_mean_24',
            'Holiday', 'WeekDay',
            'hour', 'day_of_week', 'month', 'day_of_year',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos', 'month_sin', 'month_cos',
            'demand_lag_1', 'demand_lag_24', 'demand_lag_168',
            'demand_rolling_mean_24', 'demand_rolling_std_24',
            'demand_rolling_mean_168', 'demand_rolling_std_168'
        ]
        
        # Remove rows with NaN values (due to lag and rolling features)
        self.data = self.data.dropna()
        
        # Prepare X and y
        self.X = self.data[feature_cols].copy()
        self.y = self.data['Demand'].copy()
        
        print(f"Features: {len(feature_cols)}")
        print(f"Samples after feature engineering: {len(self.X)}")
        
        return self.X, self.y
    
    def split_data(self, test_size=0.2):
        """Split data using time-based approach"""
        print(f"\n📊 Splitting data (test_size={test_size})...")
        
        # Time-based split to maintain temporal order
        split_idx = int(len(self.X) * (1 - test_size))
        
        self.X_train = self.X.iloc[:split_idx].copy()
        self.X_test = self.X.iloc[split_idx:].copy()
        self.y_train = self.y.iloc[:split_idx].copy()
        self.y_test = self.y.iloc[split_idx:].copy()
        
        print(f"Training set: {len(self.X_train)} samples")
        print(f"Test set: {len(self.X_test)} samples")
        print(f"Training period: {self.X_train.index.min()} to {self.X_train.index.max()}")
        print(f"Test period: {self.X_test.index.min()} to {self.X_test.index.max()}")
        
        # Scale features for Linear Regression
        print("🔧 Scaling features...")
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
        return self.X_train, self.X_test, self.y_train, self.y_test
    
    def initialize_models(self):
        """Initialize the 4 regression models"""
        print("\n🤖 Initializing regression models...")
        
        self.models = {
            'Linear Regression': LinearRegression(),
            'Random Forest': RandomForestRegressor(random_state=42),
            'XGBoost': xgb.XGBRegressor(random_state=42, verbosity=0),
            'LightGBM': lgb.LGBMRegressor(random_state=42, verbosity=-1)
        }
        
        print(f"Models initialized: {list(self.models.keys())}")
        return self.models
    
    def cross_validation_evaluation(self, cv_folds=5):
        """Perform cross-validation for all models"""
        print(f"\n🔄 Performing {cv_folds}-fold cross-validation...")
        
        # Use TimeSeriesSplit for time series data
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        
        cv_results = {}
        
        for name, model in self.models.items():
            print(f"  Evaluating {name}...")
            
            # Determine if model needs scaled data
            if name == 'Linear Regression':
                X_cv = self.X_train_scaled
            else:
                X_cv = self.X_train
            
            # Cross-validation scores
            cv_scores = cross_val_score(model, X_cv, self.y_train, 
                                      cv=tscv, scoring='neg_mean_absolute_error', n_jobs=-1)
            
            cv_results[name] = {
                'cv_mae_scores': -cv_scores,
                'cv_mae_mean': -cv_scores.mean(),
                'cv_mae_std': cv_scores.std()
            }
            
            print(f"    CV MAE: {cv_results[name]['cv_mae_mean']:.2f} ± {cv_results[name]['cv_mae_std']:.2f}")
        
        self.results['cross_validation'] = cv_results
        return cv_results
    
    def hyperparameter_tuning(self):
        """Perform hyperparameter tuning for each model"""
        print("\n🎯 Hyperparameter tuning...")
        
        # Define parameter grids
        param_grids = {
            'Linear Regression': {},  # No hyperparameters to tune
            
            'Random Forest': {
                'n_estimators': [100, 200],
                'max_depth': [10, 20, None],
                'min_samples_split': [2, 5],
                'min_samples_leaf': [1, 2]
            },
            
            'XGBoost': {
                'n_estimators': [100, 200],
                'max_depth': [3, 6, 9],
                'learning_rate': [0.1, 0.2],
                'subsample': [0.8, 1.0]
            },
            
            'LightGBM': {
                'n_estimators': [100, 200],
                'max_depth': [3, 6, 9],
                'learning_rate': [0.1, 0.2],
                'num_leaves': [31, 50]
            }
        }
        
        # Use TimeSeriesSplit for hyperparameter tuning
        tscv = TimeSeriesSplit(n_splits=3)  # Fewer splits for faster tuning
        
        tuning_results = {}
        
        for name, model in self.models.items():
            print(f"  Tuning {name}...")
            
            if name == 'Linear Regression':
                # No tuning needed for Linear Regression
                self.best_models[name] = model
                tuning_results[name] = {'best_params': {}, 'best_score': None}
                continue
            
            # Determine if model needs scaled data
            X_tune = self.X_train_scaled if name == 'Linear Regression' else self.X_train
            
            # Grid search
            grid_search = GridSearchCV(
                model, 
                param_grids[name],
                cv=tscv,
                scoring='neg_mean_absolute_error',
                n_jobs=-1,
                verbose=0
            )
            
            grid_search.fit(X_tune, self.y_train)
            
            # Store best model
            self.best_models[name] = grid_search.best_estimator_
            
            tuning_results[name] = {
                'best_params': grid_search.best_params_,
                'best_score': -grid_search.best_score_
            }
            
            print(f"    Best MAE: {tuning_results[name]['best_score']:.2f}")
            print(f"    Best params: {tuning_results[name]['best_params']}")
        
        self.results['hyperparameter_tuning'] = tuning_results
        return tuning_results

    def train_and_evaluate_models(self):
        """Train best models and evaluate on test set"""
        print("\n🏋️ Training and evaluating best models...")

        test_results = {}

        for name, model in self.best_models.items():
            print(f"  Training {name}...")

            # Determine if model needs scaled data
            if name == 'Linear Regression':
                X_train_use = self.X_train_scaled
                X_test_use = self.X_test_scaled
            else:
                X_train_use = self.X_train
                X_test_use = self.X_test

            # Train model
            model.fit(X_train_use, self.y_train)

            # Make predictions
            y_train_pred = model.predict(X_train_use)
            y_test_pred = model.predict(X_test_use)

            # Calculate metrics
            train_mae = mean_absolute_error(self.y_train, y_train_pred)
            train_rmse = np.sqrt(mean_squared_error(self.y_train, y_train_pred))
            train_r2 = r2_score(self.y_train, y_train_pred)

            test_mae = mean_absolute_error(self.y_test, y_test_pred)
            test_rmse = np.sqrt(mean_squared_error(self.y_test, y_test_pred))
            test_r2 = r2_score(self.y_test, y_test_pred)
            test_mape = np.mean(np.abs((self.y_test - y_test_pred) / self.y_test)) * 100

            test_results[name] = {
                'train_mae': train_mae,
                'train_rmse': train_rmse,
                'train_r2': train_r2,
                'test_mae': test_mae,
                'test_rmse': test_rmse,
                'test_r2': test_r2,
                'test_mape': test_mape,
                'y_test_pred': y_test_pred
            }

            print(f"    Test MAE: {test_mae:.2f}, Test R²: {test_r2:.4f}, Test MAPE: {test_mape:.2f}%")

        self.results['test_evaluation'] = test_results
        return test_results

    def find_best_model(self):
        """Find the best performing model based on test MAE"""
        print("\n🏆 Finding best model...")

        test_results = self.results['test_evaluation']

        # Find model with lowest test MAE
        best_mae = float('inf')
        best_model_name = None

        for name, results in test_results.items():
            if results['test_mae'] < best_mae:
                best_mae = results['test_mae']
                best_model_name = name

        self.best_model_name = best_model_name
        self.best_model = self.best_models[best_model_name]

        print(f"🥇 Best Model: {best_model_name}")
        print(f"📊 Best Test MAE: {best_mae:.2f}")
        print(f"📊 Best Test R²: {test_results[best_model_name]['test_r2']:.4f}")
        print(f"📊 Best Test MAPE: {test_results[best_model_name]['test_mape']:.2f}%")

        return best_model_name, self.best_model

    def create_best_model_visualizations(self):
        """Create two separate visualizations for the best model only"""
        print("\n🎨 Creating best model visualizations...")

        best_name = self.best_model_name
        best_results = self.results['test_evaluation'][best_name]
        y_pred = best_results['y_test_pred']

        # Visualization 1: Time Series Plot (Actual vs Predicted)
        plt.figure(figsize=(14, 8))
        plt.plot(self.y_test.index, self.y_test.values, 'b-', label='Actual', linewidth=2.5, alpha=0.8)
        plt.plot(self.y_test.index, y_pred, 'r-', label='Predicted', linewidth=2.5, alpha=0.8)

        plt.title(f'Best Model ({best_name}): Actual vs Predicted Electric Demand',
                 fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('Date', fontsize=14, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=14, fontweight='bold')
        plt.legend(fontsize=12, loc='upper right')
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)

        # Add performance metrics as text box
        metrics_text = f'Performance Metrics:\nMAE = {best_results["test_mae"]:.2f} kW\nRMSE = {best_results["test_rmse"]:.2f} kW\nR² = {best_results["test_r2"]:.4f}\nMAPE = {best_results["test_mape"]:.2f}%'
        plt.text(0.02, 0.98, metrics_text, transform=plt.gca().transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8),
                fontsize=11, fontweight='bold')

        plt.tight_layout()
        plt.savefig('best_model_actual_vs_predicted.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Visualization 2: Scatter Plot
        plt.figure(figsize=(10, 10))
        plt.scatter(self.y_test, y_pred, alpha=0.6, s=15, color='blue', edgecolors='navy', linewidth=0.5)

        # Perfect prediction line
        min_val = min(self.y_test.min(), y_pred.min())
        max_val = max(self.y_test.max(), y_pred.max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=3, alpha=0.8, label='Perfect Prediction')

        plt.xlabel('Actual Electric Demand (kW)', fontsize=14, fontweight='bold')
        plt.ylabel('Predicted Electric Demand (kW)', fontsize=14, fontweight='bold')
        plt.title(f'Best Model ({best_name}): Actual vs Predicted Scatter Plot',
                 fontsize=16, fontweight='bold', pad=20)
        plt.grid(True, alpha=0.3)
        plt.legend(fontsize=12)

        # Add performance metrics
        metrics_text = f'Model Performance:\nR² = {best_results["test_r2"]:.4f}\nMAE = {best_results["test_mae"]:.2f} kW\nRMSE = {best_results["test_rmse"]:.2f} kW\nMAPE = {best_results["test_mape"]:.2f}%'
        plt.text(0.05, 0.95, metrics_text, transform=plt.gca().transAxes,
                verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5', facecolor='wheat', alpha=0.9),
                fontsize=12, fontweight='bold')

        # Add correlation coefficient
        correlation = np.corrcoef(self.y_test, y_pred)[0, 1]
        corr_text = f'Correlation: {correlation:.4f}'
        plt.text(0.05, 0.05, corr_text, transform=plt.gca().transAxes,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgreen', alpha=0.8),
                fontsize=11, fontweight='bold')

        plt.tight_layout()
        plt.savefig('best_model_scatter_plot.png', dpi=300, bbox_inches='tight')
        plt.show()

        return correlation

    def print_comprehensive_summary(self):
        """Print comprehensive results summary"""
        print("\n" + "="*80)
        print("📊 ELECTRIC DEMAND REGRESSION ANALYSIS RESULTS")
        print("="*80)

        # Cross-validation results
        print("\n🔄 Cross-Validation Results:")
        cv_results = self.results['cross_validation']
        for model, results in cv_results.items():
            print(f"  {model:15}: CV MAE = {results['cv_mae_mean']:8.2f} ± {results['cv_mae_std']:6.2f}")

        # Test set results
        print("\n🎯 Test Set Results:")
        test_results = self.results['test_evaluation']
        print(f"{'Model':<15} {'MAE':<10} {'RMSE':<10} {'R²':<10} {'MAPE (%)':<10}")
        print("-" * 60)

        for model, results in test_results.items():
            marker = "🥇" if model == self.best_model_name else "  "
            print(f"{marker} {model:<13} {results['test_mae']:<10.2f} {results['test_rmse']:<10.2f} "
                  f"{results['test_r2']:<10.4f} {results['test_mape']:<10.2f}")

        # Best model details
        print(f"\n🏆 BEST MODEL: {self.best_model_name}")
        best_results = test_results[self.best_model_name]
        print(f"   📊 Test MAE:  {best_results['test_mae']:.2f} kW")
        print(f"   📊 Test RMSE: {best_results['test_rmse']:.2f} kW")
        print(f"   📊 Test R²:   {best_results['test_r2']:.4f}")
        print(f"   📊 Test MAPE: {best_results['test_mape']:.2f}%")

        # Hyperparameter tuning results
        if 'hyperparameter_tuning' in self.results:
            print(f"\n🎯 Best Hyperparameters for {self.best_model_name}:")
            best_params = self.results['hyperparameter_tuning'][self.best_model_name]['best_params']
            if best_params:
                for param, value in best_params.items():
                    print(f"   {param}: {value}")
            else:
                print("   No hyperparameters tuned (Linear Regression)")

        print("\n" + "="*80)

    def run_complete_regression_analysis(self):
        """Run the complete regression analysis pipeline"""
        print("🚀 STARTING COMPLETE REGRESSION ANALYSIS")
        print("Algorithms: XGBoost, Random Forest, LightGBM, Linear Regression")
        print("="*80)

        try:
            # Step 1: Data preparation
            self.load_and_prepare_data()

            # Step 2: Data splitting
            self.split_data()

            # Step 3: Initialize models
            self.initialize_models()

            # Step 4: Cross-validation evaluation
            self.cross_validation_evaluation()

            # Step 5: Hyperparameter tuning
            self.hyperparameter_tuning()

            # Step 6: Train and evaluate models
            self.train_and_evaluate_models()

            # Step 7: Find best model
            self.find_best_model()

            # Step 8: Create visualizations for best model
            correlation = self.create_best_model_visualizations()

            # Step 9: Print comprehensive summary
            self.print_comprehensive_summary()

            print(f"\n✅ REGRESSION ANALYSIS COMPLETED SUCCESSFULLY!")
            print(f"🏆 Best Model: {self.best_model_name}")
            print(f"📁 Visualizations saved:")
            print(f"   • best_model_actual_vs_predicted.png")
            print(f"   • best_model_scatter_plot.png")

            return self.results, self.best_model_name, self.best_model

        except Exception as e:
            print(f"❌ Error during regression analysis: {e}")
            import traceback
            traceback.print_exc()
            return None, None, None


def main():
    """Main execution function for regression analysis"""
    print("🤖 ELECTRIC DEMAND REGRESSION ANALYSIS")
    print("Advanced Machine Learning with Cross-Validation and Hyperparameter Tuning")
    print("="*80)

    # Initialize regression analyzer
    analyzer = RegressionAnalysis('electric_demand_1h.csv')

    # Run complete analysis
    results, best_model_name, best_model = analyzer.run_complete_regression_analysis()

    if results is not None:
        print(f"\n🎉 SUCCESS! Regression analysis completed.")
        print(f"🏆 Best performing model: {best_model_name}")

        # Additional insights
        test_results = results['test_evaluation']
        best_mae = test_results[best_model_name]['test_mae']
        best_r2 = test_results[best_model_name]['test_r2']
        best_mape = test_results[best_model_name]['test_mape']

        print(f"\n📊 Best Model Performance:")
        print(f"   MAE:  {best_mae:.2f} kW")
        print(f"   R²:   {best_r2:.4f}")
        print(f"   MAPE: {best_mape:.2f}%")

        # Performance interpretation
        if best_mape < 5:
            performance = "Excellent"
        elif best_mape < 10:
            performance = "Very Good"
        elif best_mape < 15:
            performance = "Good"
        elif best_mape < 25:
            performance = "Fair"
        else:
            performance = "Needs Improvement"

        print(f"   Performance Rating: {performance}")

        # Model ranking
        print(f"\n📈 Model Ranking (by MAE):")
        sorted_models = sorted(test_results.items(), key=lambda x: x[1]['test_mae'])
        for i, (model, metrics) in enumerate(sorted_models, 1):
            emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "  "
            print(f"   {emoji} {i}. {model}: MAE={metrics['test_mae']:.2f}, R²={metrics['test_r2']:.4f}")

        print(f"\n📁 Generated files:")
        print(f"   • best_model_actual_vs_predicted.png")
        print(f"   • best_model_scatter_plot.png")

    else:
        print("❌ Regression analysis failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
