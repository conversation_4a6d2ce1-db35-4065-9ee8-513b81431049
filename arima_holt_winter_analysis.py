"""
ARIMA vs Holt-Winter Electric Demand Forecasting Analysis
Complete 3-Phase Methodology with Enhanced Features

Phase 1: Data Preparation & EDA
Phase 2: Model Training & Testing  
Phase 3: Final Validation & Comparison
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Time Series Libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller

# Metrics
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.preprocessing import StandardScaler

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ArimaHoltWinterAnalysis:
    def __init__(self, data_path):
        """Initialize the forecasting analyzer"""
        self.data_path = data_path
        self.data = None
        self.train_data = None
        self.test_data = None
        self.validation_data = None
        self.results = {}
        self.scaler = StandardScaler()
        
    def phase1_data_preparation(self):
        """Phase 1: Enhanced Data Preparation & EDA"""
        print("🚀 ARIMA vs HOLT-WINTER FORECASTING ANALYSIS")
        print("="*60)
        print("📊 PHASE 1: DATA PREPARATION & EDA")
        print("="*60)
        
        # Load data
        print("Loading electric demand data...")
        self.data = pd.read_csv(self.data_path)
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        self.data = self.data.asfreq('H')  # Set hourly frequency
        
        print(f"Data shape: {self.data.shape}")
        print(f"Date range: {self.data.index.min()} to {self.data.index.max()}")
        
        # Enhanced feature engineering for exogenous variables
        print("\nCreating enhanced exogenous variables...")
        
        # Cyclical time features
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data.index.hour / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data.index.hour / 24)
        self.data['day_sin'] = np.sin(2 * np.pi * self.data.index.dayofweek / 7)
        self.data['day_cos'] = np.cos(2 * np.pi * self.data.index.dayofweek / 7)
        self.data['month_sin'] = np.sin(2 * np.pi * self.data.index.month / 12)
        self.data['month_cos'] = np.cos(2 * np.pi * self.data.index.month / 12)
        
        # Scale temperature
        temp_scaled = self.scaler.fit_transform(self.data[['Temperature']])
        self.data['Temperature_scaled'] = temp_scaled.flatten()
        
        # Exogenous variables for SARIMAX
        self.exog_cols = ['Temperature_scaled', 'Holiday', 'hour_sin', 'hour_cos', 
                         'day_sin', 'day_cos', 'month_sin', 'month_cos']
        self.exog_data = self.data[self.exog_cols].copy()
        
        print(f"Exogenous variables: {self.exog_cols}")
        
        # Basic EDA
        self.perform_eda()
        
        # Chronological data splitting (70% train, 20% test, 10% validation)
        self.split_data_chronologically()
        
        return self.data
    def perform_eda(self):
        """Perform exploratory data analysis"""
        print("\n📈 Exploratory Data Analysis...")
        
        # Basic statistics
        print(f"\nDemand Statistics:")
        print(self.data['Demand'].describe())
        
        # Stationarity test
        adf_result = adfuller(self.data['Demand'].dropna())
        print(f"\nStationarity Test (ADF):")
        print(f"ADF Statistic: {adf_result[0]:.6f}")
        print(f"p-value: {adf_result[1]:.6f}")
        
        if adf_result[1] <= 0.05:
            print("✓ Series is stationary")
        else:
            print("✗ Series is non-stationary")
        
        # Create EDA visualization
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # Time series plot
        axes[0,0].plot(self.data.index, self.data['Demand'], alpha=0.7, linewidth=0.5)
        axes[0,0].set_title('Electric Demand Time Series', fontsize=14, fontweight='bold')
        axes[0,0].set_ylabel('Demand (kW)')
        axes[0,0].grid(True, alpha=0.3)
        
        # Daily pattern
        hourly_pattern = self.data.groupby(self.data.index.hour)['Demand'].mean()
        axes[0,1].plot(hourly_pattern.index, hourly_pattern.values, 'b-', linewidth=2)
        axes[0,1].set_title('Average Daily Demand Pattern', fontsize=14, fontweight='bold')
        axes[0,1].set_xlabel('Hour of Day')
        axes[0,1].set_ylabel('Average Demand (kW)')
        axes[0,1].grid(True, alpha=0.3)
        
        # Weekly pattern
        weekly_pattern = self.data.groupby(self.data.index.dayofweek)['Demand'].mean()
        days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        axes[1,0].bar(range(7), weekly_pattern.values)
        axes[1,0].set_title('Average Weekly Demand Pattern', fontsize=14, fontweight='bold')
        axes[1,0].set_xlabel('Day of Week')
        axes[1,0].set_ylabel('Average Demand (kW)')
        axes[1,0].set_xticks(range(7))
        axes[1,0].set_xticklabels(days)
        axes[1,0].grid(True, alpha=0.3)
        
        # Temperature vs Demand
        axes[1,1].scatter(self.data['Temperature'], self.data['Demand'], alpha=0.1, s=1)
        axes[1,1].set_title('Temperature vs Demand Relationship', fontsize=14, fontweight='bold')
        axes[1,1].set_xlabel('Temperature (°C)')
        axes[1,1].set_ylabel('Demand (kW)')
        axes[1,1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('arima_holt_winter_eda.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def split_data_chronologically(self):
        """Split data chronologically"""
        print("\n📊 Chronological Data Splitting...")
        
        total_len = len(self.data)
        train_end = int(0.7 * total_len)
        test_end = int(0.9 * total_len)
        
        self.train_data = self.data.iloc[:train_end].copy()
        self.test_data = self.data.iloc[train_end:test_end].copy()
        self.validation_data = self.data.iloc[test_end:].copy()
        
        # Split exogenous data
        self.train_exog = self.exog_data.iloc[:train_end].copy()
        self.test_exog = self.exog_data.iloc[train_end:test_end].copy()
        self.validation_exog = self.exog_data.iloc[test_end:].copy()
        
        print(f"Training: {len(self.train_data)} samples ({len(self.train_data)/total_len*100:.1f}%)")
        print(f"  Period: {self.train_data.index.min()} to {self.train_data.index.max()}")
        print(f"Test: {len(self.test_data)} samples ({len(self.test_data)/total_len*100:.1f}%)")
        print(f"  Period: {self.test_data.index.min()} to {self.test_data.index.max()}")
        print(f"Validation: {len(self.validation_data)} samples ({len(self.validation_data)/total_len*100:.1f}%)")
        print(f"  Period: {self.validation_data.index.min()} to {self.validation_data.index.max()}")
    
    def phase2_model_training(self):
        """Phase 2: Model Training & Testing"""
        print("\n" + "="*60)
        print("📊 PHASE 2: MODEL TRAINING & TESTING")
        print("="*60)
        
        # Train SARIMAX model
        print("Training SARIMAX model...")
        try:
            self.sarimax_model = SARIMAX(
                self.train_data['Demand'],
                exog=self.train_exog,
                order=(1, 1, 1),           # ARIMA order
                seasonal_order=(1, 1, 1, 24),  # Seasonal order (24-hour cycle)
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            self.sarimax_fitted = self.sarimax_model.fit(disp=False, maxiter=100)
            print(f"✓ SARIMAX trained - AIC: {self.sarimax_fitted.aic:.2f}")
            
        except Exception as e:
            print(f"SARIMAX training failed: {e}")
            # Fallback to simple ARIMA
            self.sarimax_model = ARIMA(self.train_data['Demand'], order=(2, 1, 2))
            self.sarimax_fitted = self.sarimax_model.fit()
            print("✓ Using ARIMA fallback")
        
        # Train Holt-Winter model
        print("Training Holt-Winter model...")
        try:
            self.holt_winter_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24,
                initialization_method='estimated'
            )
            self.holt_winter_fitted = self.holt_winter_model.fit(
                optimized=True,
                remove_bias=True
            )
            print(f"✓ Holt-Winter trained - AIC: {self.holt_winter_fitted.aic:.2f}")
            
        except Exception as e:
            print(f"Holt-Winter optimization failed: {e}")
            # Simple version
            self.holt_winter_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24
            )
            self.holt_winter_fitted = self.holt_winter_model.fit()
            print("✓ Using simple Holt-Winter")
        
        # Test set evaluation
        self.evaluate_test_set()
    
    def evaluate_test_set(self):
        """Evaluate models on test set"""
        print("\n📊 Test Set Evaluation...")
        
        test_steps = len(self.test_data)
        
        # SARIMAX forecasting
        try:
            if hasattr(self.sarimax_fitted, 'get_forecast'):
                forecast_result = self.sarimax_fitted.get_forecast(
                    steps=test_steps,
                    exog=self.test_exog
                )
                sarimax_forecast = forecast_result.predicted_mean
            else:
                sarimax_forecast = self.sarimax_fitted.forecast(steps=test_steps)
            sarimax_forecast.index = self.test_data.index
            
        except Exception as e:
            print(f"SARIMAX forecast error: {e}")
            sarimax_forecast = pd.Series([self.train_data['Demand'].mean()] * test_steps,
                                       index=self.test_data.index)
        
        # Holt-Winter forecasting
        try:
            hw_forecast = self.holt_winter_fitted.forecast(steps=test_steps)
            hw_forecast.index = self.test_data.index
        except Exception as e:
            print(f"Holt-Winter forecast error: {e}")
            hw_forecast = pd.Series([self.train_data['Demand'].mean()] * test_steps,
                                  index=self.test_data.index)
        
        # Calculate metrics
        actual_test = self.test_data['Demand']
        
        def calc_metrics(actual, predicted):
            mae = mean_absolute_error(actual, predicted)
            rmse = np.sqrt(mean_squared_error(actual, predicted))
            mape = np.mean(np.abs((actual - predicted) / actual)) * 100
            return {'MAE': mae, 'RMSE': rmse, 'MAPE': mape}
        
        sarimax_metrics = calc_metrics(actual_test, sarimax_forecast)
        hw_metrics = calc_metrics(actual_test, hw_forecast)
        
        print(f"\nTest Set Results:")
        print(f"SARIMAX: MAE={sarimax_metrics['MAE']:.2f}, MAPE={sarimax_metrics['MAPE']:.2f}%")
        print(f"Holt-Winter: MAE={hw_metrics['MAE']:.2f}, MAPE={hw_metrics['MAPE']:.2f}%")
        
        # Store results
        self.results['test_metrics'] = {
            'SARIMAX': sarimax_metrics,
            'Holt-Winter': hw_metrics
        }
        self.results['test_forecasts'] = {
            'actual': actual_test,
            'sarimax': sarimax_forecast,
            'holt_winter': hw_forecast
        }

    def phase3_final_validation(self):
        """Phase 3: Final Validation & Comparison"""
        print("\n" + "="*60)
        print("📊 PHASE 3: FINAL VALIDATION & COMPARISON")
        print("="*60)

        # Retrain on combined data (train + test)
        combined_data = pd.concat([self.train_data, self.test_data])
        combined_exog = pd.concat([self.train_exog, self.test_exog])

        print(f"Retraining on {len(combined_data)} samples...")

        # Retrain SARIMAX
        try:
            self.sarimax_final = SARIMAX(
                combined_data['Demand'],
                exog=combined_exog,
                order=(1, 1, 1),
                seasonal_order=(1, 1, 1, 24),
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            self.sarimax_final_fitted = self.sarimax_final.fit(disp=False, maxiter=50)
            print("✓ SARIMAX retrained")
        except:
            self.sarimax_final_fitted = self.sarimax_fitted
            print("✓ Using previous SARIMAX model")

        # Retrain Holt-Winter
        try:
            self.holt_winter_final = ExponentialSmoothing(
                combined_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24
            )
            self.holt_winter_final_fitted = self.holt_winter_final.fit(optimized=True)
            print("✓ Holt-Winter retrained")
        except:
            self.holt_winter_final_fitted = self.holt_winter_fitted
            print("✓ Using previous Holt-Winter model")

        # Multi-horizon validation
        validation_steps = len(self.validation_data)
        horizons = {
            '24h': min(24, validation_steps),
            '168h': min(168, validation_steps),
            'full': validation_steps
        }

        validation_results = {}

        for horizon_name, steps in horizons.items():
            print(f"\nValidating {horizon_name} horizon ({steps} steps)...")

            # SARIMAX validation forecast
            try:
                if hasattr(self.sarimax_final_fitted, 'get_forecast'):
                    val_forecast_result = self.sarimax_final_fitted.get_forecast(
                        steps=steps,
                        exog=self.validation_exog.iloc[:steps]
                    )
                    sarimax_val_forecast = val_forecast_result.predicted_mean
                else:
                    sarimax_val_forecast = self.sarimax_final_fitted.forecast(steps=steps)
                sarimax_val_forecast.index = self.validation_data.index[:steps]
            except Exception as e:
                print(f"SARIMAX validation error: {e}")
                sarimax_val_forecast = pd.Series([combined_data['Demand'].mean()] * steps,
                                               index=self.validation_data.index[:steps])

            # Holt-Winter validation forecast
            try:
                hw_val_forecast = self.holt_winter_final_fitted.forecast(steps=steps)
                hw_val_forecast.index = self.validation_data.index[:steps]
            except Exception as e:
                print(f"Holt-Winter validation error: {e}")
                hw_val_forecast = pd.Series([combined_data['Demand'].mean()] * steps,
                                          index=self.validation_data.index[:steps])

            # Calculate validation metrics
            actual_val = self.validation_data['Demand'].iloc[:steps]

            def calc_metrics(actual, predicted):
                mae = mean_absolute_error(actual, predicted)
                rmse = np.sqrt(mean_squared_error(actual, predicted))
                mape = np.mean(np.abs((actual - predicted) / actual)) * 100
                return {'MAE': mae, 'RMSE': rmse, 'MAPE': mape}

            sarimax_val_metrics = calc_metrics(actual_val, sarimax_val_forecast)
            hw_val_metrics = calc_metrics(actual_val, hw_val_forecast)

            validation_results[horizon_name] = {
                'SARIMAX': sarimax_val_metrics,
                'Holt-Winter': hw_val_metrics,
                'forecasts': {
                    'actual': actual_val,
                    'sarimax': sarimax_val_forecast,
                    'holt_winter': hw_val_forecast
                }
            }

            print(f"  SARIMAX: MAE={sarimax_val_metrics['MAE']:.2f}, MAPE={sarimax_val_metrics['MAPE']:.2f}%")
            print(f"  Holt-Winter: MAE={hw_val_metrics['MAE']:.2f}, MAPE={hw_val_metrics['MAPE']:.2f}%")

        self.results['validation_metrics'] = validation_results
        return validation_results

    def create_comprehensive_visualization(self):
        """Create comprehensive comparison visualization"""
        print("\n🎨 Creating comprehensive visualization...")

        fig, axes = plt.subplots(2, 3, figsize=(20, 12))

        # Test set comparison
        test_data = self.results['test_forecasts']
        axes[0,0].plot(test_data['actual'].index, test_data['actual'].values,
                      'k-', label='Actual', linewidth=2)
        axes[0,0].plot(test_data['sarimax'].index, test_data['sarimax'].values,
                      'b-', label='SARIMAX', alpha=0.8)
        axes[0,0].plot(test_data['holt_winter'].index, test_data['holt_winter'].values,
                      'r-', label='Holt-Winter', alpha=0.8)
        axes[0,0].set_title('Test Set: Actual vs Forecasts', fontsize=14, fontweight='bold')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)
        axes[0,0].tick_params(axis='x', rotation=45)

        # 24h validation
        if '24h' in self.results['validation_metrics']:
            val_24h = self.results['validation_metrics']['24h']['forecasts']
            axes[0,1].plot(val_24h['actual'].index, val_24h['actual'].values,
                          'k-', label='Actual', linewidth=2)
            axes[0,1].plot(val_24h['sarimax'].index, val_24h['sarimax'].values,
                          'b-', label='SARIMAX', alpha=0.8)
            axes[0,1].plot(val_24h['holt_winter'].index, val_24h['holt_winter'].values,
                          'r-', label='Holt-Winter', alpha=0.8)
            axes[0,1].set_title('24h Validation Forecast', fontsize=14, fontweight='bold')
            axes[0,1].legend()
            axes[0,1].grid(True, alpha=0.3)
            axes[0,1].tick_params(axis='x', rotation=45)

        # 168h validation
        if '168h' in self.results['validation_metrics']:
            val_168h = self.results['validation_metrics']['168h']['forecasts']
            axes[0,2].plot(val_168h['actual'].index, val_168h['actual'].values,
                          'k-', label='Actual', linewidth=2)
            axes[0,2].plot(val_168h['sarimax'].index, val_168h['sarimax'].values,
                          'b-', label='SARIMAX', alpha=0.8)
            axes[0,2].plot(val_168h['holt_winter'].index, val_168h['holt_winter'].values,
                          'r-', label='Holt-Winter', alpha=0.8)
            axes[0,2].set_title('168h (1 Week) Validation Forecast', fontsize=14, fontweight='bold')
            axes[0,2].legend()
            axes[0,2].grid(True, alpha=0.3)
            axes[0,2].tick_params(axis='x', rotation=45)

        # Performance comparison - MAE
        horizons = list(self.results['validation_metrics'].keys())
        sarimax_maes = [self.results['validation_metrics'][h]['SARIMAX']['MAE'] for h in horizons]
        hw_maes = [self.results['validation_metrics'][h]['Holt-Winter']['MAE'] for h in horizons]

        x = np.arange(len(horizons))
        width = 0.35

        bars1 = axes[1,0].bar(x - width/2, sarimax_maes, width, label='SARIMAX', alpha=0.8, color='blue')
        bars2 = axes[1,0].bar(x + width/2, hw_maes, width, label='Holt-Winter', alpha=0.8, color='red')

        # Add value labels
        for bar in bars1:
            height = bar.get_height()
            axes[1,0].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{height:.0f}', ha='center', va='bottom', fontweight='bold')
        for bar in bars2:
            height = bar.get_height()
            axes[1,0].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{height:.0f}', ha='center', va='bottom', fontweight='bold')

        axes[1,0].set_xlabel('Forecast Horizon')
        axes[1,0].set_ylabel('Mean Absolute Error (MAE)')
        axes[1,0].set_title('MAE Comparison Across Horizons', fontsize=14, fontweight='bold')
        axes[1,0].set_xticks(x)
        axes[1,0].set_xticklabels(horizons)
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)

        # Performance comparison - MAPE
        sarimax_mapes = [self.results['validation_metrics'][h]['SARIMAX']['MAPE'] for h in horizons]
        hw_mapes = [self.results['validation_metrics'][h]['Holt-Winter']['MAPE'] for h in horizons]

        bars3 = axes[1,1].bar(x - width/2, sarimax_mapes, width, label='SARIMAX', alpha=0.8, color='blue')
        bars4 = axes[1,1].bar(x + width/2, hw_mapes, width, label='Holt-Winter', alpha=0.8, color='red')

        # Add value labels
        for bar in bars3:
            height = bar.get_height()
            axes[1,1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')
        for bar in bars4:
            height = bar.get_height()
            axes[1,1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{height:.1f}%', ha='center', va='bottom', fontweight='bold')

        axes[1,1].set_xlabel('Forecast Horizon')
        axes[1,1].set_ylabel('Mean Absolute Percentage Error (MAPE %)')
        axes[1,1].set_title('MAPE Comparison Across Horizons', fontsize=14, fontweight='bold')
        axes[1,1].set_xticks(x)
        axes[1,1].set_xticklabels(horizons)
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)

        # Overall performance summary
        avg_sarimax_mae = np.mean(sarimax_maes)
        avg_hw_mae = np.mean(hw_maes)
        avg_sarimax_mape = np.mean(sarimax_mapes)
        avg_hw_mape = np.mean(hw_mapes)

        summary_text = f'Overall Performance Summary:\n\n'
        summary_text += f'SARIMAX:\n  Avg MAE: {avg_sarimax_mae:.2f}\n  Avg MAPE: {avg_sarimax_mape:.2f}%\n\n'
        summary_text += f'Holt-Winter:\n  Avg MAE: {avg_hw_mae:.2f}\n  Avg MAPE: {avg_hw_mape:.2f}%\n\n'

        if avg_hw_mae < avg_sarimax_mae:
            winner = 'Holt-Winter'
            improvement = (avg_sarimax_mae - avg_hw_mae) / avg_sarimax_mae * 100
        else:
            winner = 'SARIMAX'
            improvement = (avg_hw_mae - avg_sarimax_mae) / avg_hw_mae * 100

        summary_text += f'Winner: {winner}\nImprovement: {improvement:.1f}%'

        axes[1,2].text(0.1, 0.5, summary_text, transform=axes[1,2].transAxes,
                      fontsize=12, verticalalignment='center',
                      bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
        axes[1,2].set_title('Performance Summary', fontsize=14, fontweight='bold')
        axes[1,2].axis('off')

        plt.tight_layout()
        plt.savefig('arima_holt_winter_results.png', dpi=300, bbox_inches='tight')
        plt.show()

        return winner, improvement

    def print_final_summary(self):
        """Print comprehensive final summary"""
        print("\n" + "="*80)
        print("📊 ARIMA vs HOLT-WINTER ANALYSIS RESULTS")
        print("="*80)

        # Test set results
        print("\n🎯 Test Set Performance:")
        test_metrics = self.results['test_metrics']
        for method, metrics in test_metrics.items():
            print(f"  {method:12}: MAE={metrics['MAE']:8.2f}, RMSE={metrics['RMSE']:8.2f}, MAPE={metrics['MAPE']:6.2f}%")

        # Validation results
        print("\n🔍 Validation Performance by Horizon:")
        validation_metrics = self.results['validation_metrics']

        print(f"{'Horizon':<10} {'Method':<12} {'MAE':<10} {'RMSE':<10} {'MAPE (%)':<10}")
        print("-" * 65)

        for horizon, results in validation_metrics.items():
            for method, metrics in results.items():
                if method != 'forecasts':
                    print(f"{horizon:<10} {method:<12} {metrics['MAE']:<10.2f} {metrics['RMSE']:<10.2f} {metrics['MAPE']:<10.2f}")

        # Determine overall winner
        horizons = list(validation_metrics.keys())
        sarimax_maes = [validation_metrics[h]['SARIMAX']['MAE'] for h in horizons]
        hw_maes = [validation_metrics[h]['Holt-Winter']['MAE'] for h in horizons]

        avg_sarimax_mae = np.mean(sarimax_maes)
        avg_hw_mae = np.mean(hw_maes)

        if avg_hw_mae < avg_sarimax_mae:
            winner = 'Holt-Winter'
            improvement = (avg_sarimax_mae - avg_hw_mae) / avg_sarimax_mae * 100
        else:
            winner = 'SARIMAX'
            improvement = (avg_hw_mae - avg_sarimax_mae) / avg_hw_mae * 100

        print(f"\n🏆 OVERALL WINNER: {winner}")
        print(f"📊 Average MAE - SARIMAX: {avg_sarimax_mae:.2f}")
        print(f"📊 Average MAE - Holt-Winter: {avg_hw_mae:.2f}")
        print(f"🚀 Performance Improvement: {improvement:.1f}%")

        # Best performance by horizon
        print(f"\n🎯 Best Performance by Horizon:")
        for horizon in horizons:
            sarimax_mae = validation_metrics[horizon]['SARIMAX']['MAE']
            hw_mae = validation_metrics[horizon]['Holt-Winter']['MAE']
            sarimax_mape = validation_metrics[horizon]['SARIMAX']['MAPE']
            hw_mape = validation_metrics[horizon]['Holt-Winter']['MAPE']

            if hw_mae < sarimax_mae:
                best_method = 'Holt-Winter'
                best_mae = hw_mae
                best_mape = hw_mape
            else:
                best_method = 'SARIMAX'
                best_mae = sarimax_mae
                best_mape = sarimax_mape

            print(f"  {horizon:6}: {best_method} (MAE={best_mae:.2f}, MAPE={best_mape:.2f}%)")

        print("="*80)

    def run_complete_analysis(self):
        """Run the complete 3-phase analysis"""
        print("🚀 STARTING COMPLETE ARIMA vs HOLT-WINTER ANALYSIS")
        print("3-Phase Methodology: Data Preparation → Model Training → Final Validation")
        print("="*80)

        try:
            # Phase 1: Data Preparation & EDA
            self.phase1_data_preparation()

            # Phase 2: Model Training & Testing
            self.phase2_model_training()

            # Phase 3: Final Validation & Comparison
            self.phase3_final_validation()

            # Create comprehensive visualization
            winner, improvement = self.create_comprehensive_visualization()

            # Print final summary
            self.print_final_summary()

            print(f"\n✅ ANALYSIS COMPLETED SUCCESSFULLY!")
            print(f"🏆 Winner: {winner}")
            print(f"📈 Improvement: {improvement:.1f}%")
            print(f"📁 Results saved as 'arima_holt_winter_results.png'")

            return self.results, winner, improvement

        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            import traceback
            traceback.print_exc()
            return None, None, None


def main():
    """Main execution function"""
    print("🔮 ARIMA vs HOLT-WINTER FORECASTING ANALYSIS")
    print("Advanced Time Series Analysis with 3-Phase Methodology")
    print("="*80)

    # Initialize analyzer
    analyzer = ArimaHoltWinterAnalysis('electric_demand_1h.csv')

    # Run complete analysis
    results, winner, improvement = analyzer.run_complete_analysis()

    if results is not None:
        print(f"\n🎉 SUCCESS! Time series analysis completed.")
        print(f"🏆 Best method: {winner}")
        print(f"📈 Performance improvement: {improvement:.1f}%")

        # Performance summary
        validation_metrics = results['validation_metrics']

        print(f"\n📊 Key Results:")
        for horizon in ['24h', '168h']:
            if horizon in validation_metrics:
                hw_mape = validation_metrics[horizon]['Holt-Winter']['MAPE']
                sarimax_mape = validation_metrics[horizon]['SARIMAX']['MAPE']
                print(f"  {horizon:4} - Holt-Winter: {hw_mape:5.2f}% MAPE")
                print(f"  {horizon:4} - SARIMAX:     {sarimax_mape:5.2f}% MAPE")

        print(f"\n📁 Generated files:")
        print(f"  • arima_holt_winter_eda.png")
        print(f"  • arima_holt_winter_results.png")

    else:
        print("❌ Analysis failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
