"""
Step-by-Step Forecasting Analysis
ARIMA vs Holt-Winter with detailed step-by-step forecasting visualization

24-hour: t+1, t+2, t+3, ..., t+24
7-day: t+24, t+48, t+72, t+96, t+120, t+144, t+168
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Time Series Libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from sklearn.metrics import mean_absolute_error
from sklearn.preprocessing import StandardScaler

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class StepByStepForecasting:
    def __init__(self, data_path):
        """Initialize the step-by-step forecasting analyzer"""
        self.data_path = data_path
        self.data = None
        self.train_data = None
        self.test_data = None
        self.results = {}
        self.scaler = StandardScaler()
        
    def load_and_prepare_data(self):
        """Load and prepare data for step-by-step forecasting"""
        print("🔍 STEP-BY-STEP FORECASTING ANALYSIS")
        print("ARIMA vs Holt-Winter: Detailed Step Performance")
        print("="*60)
        print("📊 Loading data...")
        
        # Load data
        self.data = pd.read_csv(self.data_path)
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        self.data = self.data.asfreq('H')  # Set hourly frequency
        
        print(f"Data shape: {self.data.shape}")
        print(f"Date range: {self.data.index.min()} to {self.data.index.max()}")
        
        # Enhanced feature engineering for SARIMAX
        print("🔧 Creating exogenous variables...")
        
        # Cyclical time features
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data.index.hour / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data.index.hour / 24)
        self.data['day_sin'] = np.sin(2 * np.pi * self.data.index.dayofweek / 7)
        self.data['day_cos'] = np.cos(2 * np.pi * self.data.index.dayofweek / 7)
        
        # Scale temperature
        temp_scaled = self.scaler.fit_transform(self.data[['Temperature']])
        self.data['Temperature_scaled'] = temp_scaled.flatten()
        
        # Exogenous variables for SARIMAX
        self.exog_cols = ['Temperature_scaled', 'Holiday', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos']
        self.exog_data = self.data[self.exog_cols].copy()
        
        # Split data (use 80% for training, 20% for testing)
        split_idx = int(len(self.data) * 0.8)
        self.train_data = self.data.iloc[:split_idx].copy()
        self.test_data = self.data.iloc[split_idx:].copy()
        
        self.train_exog = self.exog_data.iloc[:split_idx].copy()
        self.test_exog = self.exog_data.iloc[split_idx:].copy()
        
        print(f"Training: {len(self.train_data)} samples")
        print(f"Test: {len(self.test_data)} samples")
        
        return self.data
    
    def train_models(self):
        """Train ARIMA and Holt-Winter models"""
        print("\n🤖 Training models...")
        
        # Train SARIMAX/ARIMA model
        print("Training ARIMA/SARIMAX...")
        try:
            self.arima_model = SARIMAX(
                self.train_data['Demand'],
                exog=self.train_exog,
                order=(1, 1, 1),           # ARIMA order
                seasonal_order=(1, 1, 1, 24),  # Seasonal order (24-hour cycle)
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            self.arima_fitted = self.arima_model.fit(disp=False, maxiter=100)
            print(f"✓ SARIMAX trained - AIC: {self.arima_fitted.aic:.2f}")
            
        except Exception as e:
            print(f"SARIMAX failed: {e}, using simple ARIMA")
            self.arima_model = ARIMA(self.train_data['Demand'], order=(2, 1, 2))
            self.arima_fitted = self.arima_model.fit()
            print("✓ ARIMA fallback trained")
        
        # Train Holt-Winter model
        print("Training Holt-Winter...")
        try:
            self.hw_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24,
                initialization_method='estimated'
            )
            self.hw_fitted = self.hw_model.fit(optimized=True, remove_bias=True)
            print(f"✓ Holt-Winter trained - AIC: {self.hw_fitted.aic:.2f}")
            
        except Exception as e:
            print(f"Optimized Holt-Winter failed: {e}, using simple version")
            self.hw_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24
            )
            self.hw_fitted = self.hw_model.fit()
            print("✓ Simple Holt-Winter trained")
    
    def perform_step_by_step_forecasting(self):
        """Perform step-by-step forecasting for both horizons"""
        print("\n📈 Performing step-by-step forecasting...")
        
        # Define forecasting steps
        steps_24h = list(range(1, 25))  # t+1 to t+24
        steps_7d = [24, 48, 72, 96, 120, 144, 168]  # t+24 to t+168 (weekly steps)
        
        # Get test data starting point
        test_start_idx = 0
        
        # Ensure we have enough test data
        max_steps = max(max(steps_24h), max(steps_7d))
        if len(self.test_data) < max_steps:
            print(f"Warning: Not enough test data. Available: {len(self.test_data)}, Required: {max_steps}")
            return
        
        # 24-hour step-by-step forecasting
        print("Generating 24-hour step-by-step forecasts...")
        results_24h = self.generate_step_forecasts(steps_24h, test_start_idx)
        
        # 7-day step-by-step forecasting  
        print("Generating 7-day step-by-step forecasts...")
        results_7d = self.generate_step_forecasts(steps_7d, test_start_idx)
        
        self.results = {
            '24h': results_24h,
            '7d': results_7d
        }
        
        return self.results
    
    def generate_step_forecasts(self, steps, start_idx):
        """Generate forecasts for specific steps"""
        results = {
            'steps': steps,
            'actual': [],
            'arima_forecasts': [],
            'hw_forecasts': [],
            'arima_errors': [],
            'hw_errors': []
        }
        
        for step in steps:
            if start_idx + step - 1 >= len(self.test_data):
                print(f"Warning: Step {step} exceeds available test data")
                break
                
            # Get actual value at step
            actual_value = self.test_data['Demand'].iloc[start_idx + step - 1]
            results['actual'].append(actual_value)
            
            # ARIMA forecast
            try:
                if hasattr(self.arima_fitted, 'get_forecast'):
                    # Use exogenous variables if available
                    if step <= len(self.test_exog):
                        exog_step = self.test_exog.iloc[start_idx:start_idx + step]
                        forecast_result = self.arima_fitted.get_forecast(steps=step, exog=exog_step)
                        arima_forecast = forecast_result.predicted_mean.iloc[-1]
                    else:
                        arima_forecast = self.arima_fitted.forecast(steps=step)[-1]
                else:
                    arima_forecast = self.arima_fitted.forecast(steps=step)[-1]
                    
            except Exception as e:
                print(f"ARIMA forecast error at step {step}: {e}")
                arima_forecast = self.train_data['Demand'].mean()
            
            results['arima_forecasts'].append(arima_forecast)
            results['arima_errors'].append(abs(actual_value - arima_forecast))
            
            # Holt-Winter forecast
            try:
                hw_forecast = self.hw_fitted.forecast(steps=step)[-1]
            except Exception as e:
                print(f"Holt-Winter forecast error at step {step}: {e}")
                hw_forecast = self.train_data['Demand'].mean()
            
            results['hw_forecasts'].append(hw_forecast)
            results['hw_errors'].append(abs(actual_value - hw_forecast))
        
        return results
    
    def create_step_by_step_visualizations(self):
        """Create detailed step-by-step visualizations"""
        print("\n🎨 Creating step-by-step visualizations...")
        
        # Create figure with 4 subplots
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        
        # 24-hour forecasting comparison
        if '24h' in self.results:
            data_24h = self.results['24h']
            steps = data_24h['steps'][:len(data_24h['actual'])]
            actual = data_24h['actual']
            arima_forecasts = data_24h['arima_forecasts']
            hw_forecasts = data_24h['hw_forecasts']
            
            # Plot 1: 24-hour Actual vs Forecasts
            axes[0,0].plot(steps, actual, 'k-o', label='Actual', linewidth=3, markersize=6)
            axes[0,0].plot(steps, arima_forecasts, 'b-s', label='ARIMA', linewidth=2, markersize=5, alpha=0.8)
            axes[0,0].plot(steps, hw_forecasts, 'r-^', label='Holt-Winter', linewidth=2, markersize=5, alpha=0.8)
            
            axes[0,0].set_title('24-Hour Step-by-Step Forecasting\nActual vs t+1, t+2, t+3, ..., t+24', 
                               fontsize=14, fontweight='bold')
            axes[0,0].set_xlabel('Forecasting Step (hours ahead)', fontsize=12, fontweight='bold')
            axes[0,0].set_ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
            axes[0,0].legend(fontsize=11)
            axes[0,0].grid(True, alpha=0.3)
            axes[0,0].set_xticks(range(1, 25, 2))
            
            # Add performance metrics
            arima_mae_24h = np.mean(data_24h['arima_errors'])
            hw_mae_24h = np.mean(data_24h['hw_errors'])
            metrics_text = f'24h Average MAE:\nARIMA: {arima_mae_24h:.2f}\nHolt-Winter: {hw_mae_24h:.2f}'
            axes[0,0].text(0.02, 0.98, metrics_text, transform=axes[0,0].transAxes,
                          verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8),
                          fontsize=10, fontweight='bold')
            
            # Plot 2: 24-hour Error Analysis
            axes[0,1].plot(steps, data_24h['arima_errors'], 'b-s', label='ARIMA Error', linewidth=2, markersize=5)
            axes[0,1].plot(steps, data_24h['hw_errors'], 'r-^', label='Holt-Winter Error', linewidth=2, markersize=5)
            
            axes[0,1].set_title('24-Hour Forecasting Errors\nAbsolute Error by Step', 
                               fontsize=14, fontweight='bold')
            axes[0,1].set_xlabel('Forecasting Step (hours ahead)', fontsize=12, fontweight='bold')
            axes[0,1].set_ylabel('Absolute Error (kW)', fontsize=12, fontweight='bold')
            axes[0,1].legend(fontsize=11)
            axes[0,1].grid(True, alpha=0.3)
            axes[0,1].set_xticks(range(1, 25, 2))
        
        # 7-day forecasting comparison
        if '7d' in self.results:
            data_7d = self.results['7d']
            steps_7d = data_7d['steps'][:len(data_7d['actual'])]
            actual_7d = data_7d['actual']
            arima_forecasts_7d = data_7d['arima_forecasts']
            hw_forecasts_7d = data_7d['hw_forecasts']
            
            # Plot 3: 7-day Actual vs Forecasts
            axes[1,0].plot(steps_7d, actual_7d, 'k-o', label='Actual', linewidth=3, markersize=8)
            axes[1,0].plot(steps_7d, arima_forecasts_7d, 'b-s', label='ARIMA', linewidth=2, markersize=6, alpha=0.8)
            axes[1,0].plot(steps_7d, hw_forecasts_7d, 'r-^', label='Holt-Winter', linewidth=2, markersize=6, alpha=0.8)
            
            axes[1,0].set_title('7-Day Step-by-Step Forecasting\nActual vs t+24, t+48, t+72, t+96, t+120, t+144, t+168', 
                               fontsize=14, fontweight='bold')
            axes[1,0].set_xlabel('Forecasting Step (hours ahead)', fontsize=12, fontweight='bold')
            axes[1,0].set_ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
            axes[1,0].legend(fontsize=11)
            axes[1,0].grid(True, alpha=0.3)
            axes[1,0].set_xticks(steps_7d)
            
            # Add day labels
            day_labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7']
            axes[1,0].set_xticklabels([f'{step}h\n{day_labels[i]}' for i, step in enumerate(steps_7d)])
            
            # Add performance metrics
            arima_mae_7d = np.mean(data_7d['arima_errors'])
            hw_mae_7d = np.mean(data_7d['hw_errors'])
            metrics_text = f'7-day Average MAE:\nARIMA: {arima_mae_7d:.2f}\nHolt-Winter: {hw_mae_7d:.2f}'
            axes[1,0].text(0.02, 0.98, metrics_text, transform=axes[1,0].transAxes,
                          verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8),
                          fontsize=10, fontweight='bold')
            
            # Plot 4: 7-day Error Analysis
            axes[1,1].plot(steps_7d, data_7d['arima_errors'], 'b-s', label='ARIMA Error', linewidth=2, markersize=6)
            axes[1,1].plot(steps_7d, data_7d['hw_errors'], 'r-^', label='Holt-Winter Error', linewidth=2, markersize=6)
            
            axes[1,1].set_title('7-Day Forecasting Errors\nAbsolute Error by Step', 
                               fontsize=14, fontweight='bold')
            axes[1,1].set_xlabel('Forecasting Step (hours ahead)', fontsize=12, fontweight='bold')
            axes[1,1].set_ylabel('Absolute Error (kW)', fontsize=12, fontweight='bold')
            axes[1,1].legend(fontsize=11)
            axes[1,1].grid(True, alpha=0.3)
            axes[1,1].set_xticks(steps_7d)
            axes[1,1].set_xticklabels([f'{step}h\n{day_labels[i]}' for i, step in enumerate(steps_7d)])
        
        plt.tight_layout()
        plt.savefig('step_by_step_forecasting_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return fig

    def print_detailed_summary(self):
        """Print detailed step-by-step performance summary"""
        print("\n" + "="*80)
        print("📊 STEP-BY-STEP FORECASTING ANALYSIS RESULTS")
        print("="*80)

        # 24-hour results
        if '24h' in self.results:
            data_24h = self.results['24h']
            print("\n🕐 24-HOUR STEP-BY-STEP PERFORMANCE:")
            print(f"{'Step':<6} {'Actual':<10} {'ARIMA':<10} {'H-W':<10} {'ARIMA Err':<12} {'H-W Err':<10}")
            print("-" * 65)

            for i in range(len(data_24h['actual'])):
                step = data_24h['steps'][i]
                actual = data_24h['actual'][i]
                arima_pred = data_24h['arima_forecasts'][i]
                hw_pred = data_24h['hw_forecasts'][i]
                arima_err = data_24h['arima_errors'][i]
                hw_err = data_24h['hw_errors'][i]

                print(f"t+{step:<3} {actual:<10.2f} {arima_pred:<10.2f} {hw_pred:<10.2f} {arima_err:<12.2f} {hw_err:<10.2f}")

            # 24-hour summary
            arima_mae_24h = np.mean(data_24h['arima_errors'])
            hw_mae_24h = np.mean(data_24h['hw_errors'])

            print(f"\n📊 24-Hour Summary:")
            print(f"   ARIMA Average MAE: {arima_mae_24h:.2f} kW")
            print(f"   Holt-Winter Average MAE: {hw_mae_24h:.2f} kW")

            if hw_mae_24h < arima_mae_24h:
                winner_24h = "Holt-Winter"
                improvement_24h = (arima_mae_24h - hw_mae_24h) / arima_mae_24h * 100
            else:
                winner_24h = "ARIMA"
                improvement_24h = (hw_mae_24h - arima_mae_24h) / hw_mae_24h * 100

            print(f"   🏆 24h Winner: {winner_24h}")
            print(f"   📈 Improvement: {improvement_24h:.1f}%")

        # 7-day results
        if '7d' in self.results:
            data_7d = self.results['7d']
            print("\n📅 7-DAY STEP-BY-STEP PERFORMANCE:")
            print(f"{'Step':<8} {'Day':<6} {'Actual':<10} {'ARIMA':<10} {'H-W':<10} {'ARIMA Err':<12} {'H-W Err':<10}")
            print("-" * 75)

            day_labels = ['Day 1', 'Day 2', 'Day 3', 'Day 4', 'Day 5', 'Day 6', 'Day 7']

            for i in range(len(data_7d['actual'])):
                step = data_7d['steps'][i]
                day = day_labels[i] if i < len(day_labels) else f"Day {i+1}"
                actual = data_7d['actual'][i]
                arima_pred = data_7d['arima_forecasts'][i]
                hw_pred = data_7d['hw_forecasts'][i]
                arima_err = data_7d['arima_errors'][i]
                hw_err = data_7d['hw_errors'][i]

                print(f"t+{step:<5} {day:<6} {actual:<10.2f} {arima_pred:<10.2f} {hw_pred:<10.2f} {arima_err:<12.2f} {hw_err:<10.2f}")

            # 7-day summary
            arima_mae_7d = np.mean(data_7d['arima_errors'])
            hw_mae_7d = np.mean(data_7d['hw_errors'])

            print(f"\n📊 7-Day Summary:")
            print(f"   ARIMA Average MAE: {arima_mae_7d:.2f} kW")
            print(f"   Holt-Winter Average MAE: {hw_mae_7d:.2f} kW")

            if hw_mae_7d < arima_mae_7d:
                winner_7d = "Holt-Winter"
                improvement_7d = (arima_mae_7d - hw_mae_7d) / arima_mae_7d * 100
            else:
                winner_7d = "ARIMA"
                improvement_7d = (hw_mae_7d - arima_mae_7d) / hw_mae_7d * 100

            print(f"   🏆 7-day Winner: {winner_7d}")
            print(f"   📈 Improvement: {improvement_7d:.1f}%")

        print("\n" + "="*80)
        print("🎯 OVERALL CONCLUSIONS:")

        if '24h' in self.results and '7d' in self.results:
            print(f"   📊 24-hour forecasting: {winner_24h} performs better")
            print(f"   📊 7-day forecasting: {winner_7d} performs better")

            # Overall recommendation
            if winner_24h == winner_7d:
                print(f"   🏆 Overall Winner: {winner_24h} (consistent across both horizons)")
            else:
                print(f"   🏆 Mixed Results: {winner_24h} for short-term, {winner_7d} for long-term")

        print("="*80)

    def run_complete_step_analysis(self):
        """Run the complete step-by-step analysis"""
        print("🔍 STARTING STEP-BY-STEP FORECASTING ANALYSIS")
        print("Detailed Performance: ARIMA vs Holt-Winter")
        print("="*80)

        try:
            # Step 1: Load and prepare data
            self.load_and_prepare_data()

            # Step 2: Train models
            self.train_models()

            # Step 3: Perform step-by-step forecasting
            self.perform_step_by_step_forecasting()

            # Step 4: Create visualizations
            self.create_step_by_step_visualizations()

            # Step 5: Print detailed summary
            self.print_detailed_summary()

            print(f"\n✅ STEP-BY-STEP ANALYSIS COMPLETED!")
            print(f"📁 Visualization saved as 'step_by_step_forecasting_analysis.png'")

            return self.results

        except Exception as e:
            print(f"❌ Error during step-by-step analysis: {e}")
            import traceback
            traceback.print_exc()
            return None


def main():
    """Main execution function"""
    print("🔍 STEP-BY-STEP FORECASTING ANALYSIS")
    print("ARIMA vs Holt-Winter: Detailed Step Performance")
    print("="*80)

    # Initialize analyzer
    analyzer = StepByStepForecasting('electric_demand_1h.csv')

    # Run complete analysis
    results = analyzer.run_complete_step_analysis()

    if results is not None:
        print(f"\n🎉 SUCCESS! Step-by-step analysis completed.")

        # Quick summary
        if '24h' in results and '7d' in results:
            arima_mae_24h = np.mean(results['24h']['arima_errors'])
            hw_mae_24h = np.mean(results['24h']['hw_errors'])
            arima_mae_7d = np.mean(results['7d']['arima_errors'])
            hw_mae_7d = np.mean(results['7d']['hw_errors'])

            print(f"\n📊 Quick Summary:")
            print(f"   24h - ARIMA: {arima_mae_24h:.2f} MAE, Holt-Winter: {hw_mae_24h:.2f} MAE")
            print(f"   7d  - ARIMA: {arima_mae_7d:.2f} MAE, Holt-Winter: {hw_mae_7d:.2f} MAE")

        print(f"\n📁 Generated file:")
        print(f"   • step_by_step_forecasting_analysis.png")

    else:
        print("❌ Step-by-step analysis failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
