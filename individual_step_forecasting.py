"""
Individual Step Forecasting Analysis
Creates separate charts for each forecasting step: t+1, t+6, t+12, t+18, t+24

Each chart shows:
- Actual Demand line
- ARIMA predictions for that specific step
- Holt-Winter predictions for that specific step
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Time Series Libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_absolute_error, mean_absolute_percentage_error
from sklearn.preprocessing import StandardScaler, MinMaxScaler

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class IndividualStepForecasting:
    def __init__(self, data_path):
        """Initialize the individual step forecasting analyzer"""
        self.data_path = data_path
        self.data = None
        self.train_data = None
        self.test_data = None
        self.arima_fitted = None
        self.hw_fitted = None
        self.arima_order = None
        self.seasonal_order = None
        self.scaler = StandardScaler()
        self.use_refitting = False  # Advanced option for model refitting
        
        # Store predictions for each step
        self.predictions_t1 = {'arima': [], 'holt_winter': []}
        self.predictions_t6 = {'arima': [], 'holt_winter': []}
        self.predictions_t12 = {'arima': [], 'holt_winter': []}
        self.predictions_t18 = {'arima': [], 'holt_winter': []}
        self.predictions_t24 = {'arima': [], 'holt_winter': []}
        
    def load_and_prepare_data(self):
        """Load and prepare data"""
        print("🔍 INDIVIDUAL STEP FORECASTING ANALYSIS")
        print("Separate charts for t+1, t+6, t+12, t+18, t+24")
        print("="*60)
        print("📊 Loading data...")
        
        # Load data
        self.data = pd.read_csv(self.data_path)
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        self.data = self.data.asfreq('H')  # Set hourly frequency
        
        print(f"Data shape: {self.data.shape}")
        print(f"Date range: {self.data.index.min()} to {self.data.index.max()}")
        
        # Enhanced feature engineering for SARIMAX
        print("🔧 Creating exogenous variables...")
        
        # Cyclical time features
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data.index.hour / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data.index.hour / 24)
        self.data['day_sin'] = np.sin(2 * np.pi * self.data.index.dayofweek / 7)
        self.data['day_cos'] = np.cos(2 * np.pi * self.data.index.dayofweek / 7)
        
        # Scale temperature
        temp_scaled = self.scaler.fit_transform(self.data[['Temperature']])
        self.data['Temperature_scaled'] = temp_scaled.flatten()
        
        # Exogenous variables for SARIMAX
        self.exog_cols = ['Temperature_scaled', 'Holiday', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos']
        self.exog_data = self.data[self.exog_cols].copy()
        
        # Split data: 60% training, 20% testing, 20% validation
        total_len = len(self.data)
        train_end = int(0.6 * total_len)
        test_end = int(0.8 * total_len)

        self.train_data = self.data.iloc[:train_end].copy()
        self.test_data = self.data.iloc[train_end:test_end].copy()
        self.validation_data = self.data.iloc[test_end:].copy()

        self.train_exog = self.exog_data.iloc[:train_end].copy()
        self.test_exog = self.exog_data.iloc[train_end:test_end].copy()
        self.validation_exog = self.exog_data.iloc[test_end:].copy()

        print(f"Training: {len(self.train_data)} samples (60%)")
        print(f"Testing: {len(self.test_data)} samples (20%)")
        print(f"Validation: {len(self.validation_data)} samples (20%)")
        
        return self.data

    def perform_acf_pacf_analysis(self):
        """Perform ACF and PACF analysis to determine ARIMA parameters manually"""
        print("\n📈 ACF/PACF Analysis for Manual ARIMA Parameter Selection...")

        # Check stationarity first
        adf_result = adfuller(self.train_data['Demand'].dropna())
        print(f"\nStationarity Test (ADF):")
        print(f"ADF Statistic: {adf_result[0]:.6f}")
        print(f"p-value: {adf_result[1]:.6f}")

        is_stationary = adf_result[1] <= 0.05
        print(f"Series is {'stationary' if is_stationary else 'non-stationary'}")

        # Determine differencing order (d)
        d = 0 if is_stationary else 1

        # Prepare series for ACF/PACF analysis
        if d == 1:
            diff_series = self.train_data['Demand'].diff().dropna()
            print("Using first-differenced series for ACF/PACF analysis")
        else:
            diff_series = self.train_data['Demand'].dropna()
            print("Using original series for ACF/PACF analysis")

        # Create ACF and PACF plots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # Original series
        axes[0,0].plot(self.train_data['Demand'])
        axes[0,0].set_title('Original Demand Series', fontsize=14, fontweight='bold')
        axes[0,0].set_ylabel('Demand (kW)')
        axes[0,0].grid(True, alpha=0.3)

        # Differenced series (if applicable)
        if d == 1:
            axes[0,1].plot(diff_series)
            axes[0,1].set_title('First-Differenced Series', fontsize=14, fontweight='bold')
            axes[0,1].set_ylabel('Differenced Demand')
            axes[0,1].grid(True, alpha=0.3)
        else:
            axes[0,1].plot(self.train_data['Demand'])
            axes[0,1].set_title('Stationary Series', fontsize=14, fontweight='bold')
            axes[0,1].set_ylabel('Demand (kW)')
            axes[0,1].grid(True, alpha=0.3)

        # ACF plot
        plot_acf(diff_series, lags=40, ax=axes[1,0], alpha=0.05)
        axes[1,0].set_title('Autocorrelation Function (ACF)', fontsize=14, fontweight='bold')
        axes[1,0].grid(True, alpha=0.3)

        # PACF plot
        plot_pacf(diff_series, lags=40, ax=axes[1,1], alpha=0.05)
        axes[1,1].set_title('Partial Autocorrelation Function (PACF)', fontsize=14, fontweight='bold')
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('acf_pacf_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Manual parameter selection based on ACF/PACF analysis
        print("\n🎯 PURE MANUAL ARIMA Parameter Selection (NO AUTO_ARIMA):")
        print("Analyzing ACF and PACF patterns from your provided plots...")
        print("📊 ACF Analysis: Strong seasonal peaks every ~24 lags, slow decay")
        print("📊 PACF Analysis: Significant spikes at lags 1-2, then sharp cutoff")

        # PURE MANUAL ANALYSIS based on your ACF/PACF plots:
        # 1. ACF shows gradual decay → suggests AR component
        # 2. PACF shows cutoff after lag 2 → suggests AR(2)
        # 3. Seasonal pattern every 24 lags in ACF → seasonal component needed
        # 4. Series is stationary → no regular differencing needed

        # Non-seasonal parameters (based purely on ACF/PACF visual inspection)
        p = 2  # AR(2) - PACF cuts off after lag 2
        d = d  # Differencing from stationarity test (0 since stationary)
        q = 1  # MA(1) - ACF shows some MA characteristics

        # Seasonal parameters (based on 24-hour seasonal pattern in ACF)
        P = 1  # Seasonal AR(1) - seasonal spikes in PACF
        D = 0  # No seasonal differencing (series already stationary)
        Q = 1  # Seasonal MA(1) - seasonal pattern in ACF
        s = 24 # Seasonal period (24 hours - clear from ACF pattern)

        print(f"📈 Manual Selection Results:")
        print(f"   Non-seasonal: AR({p}), I({d}), MA({q})")
        print(f"   Seasonal: SAR({P}), SI({D}), SMA({Q}), Period({s})")
        print(f"   ✅ NO AUTO_ARIMA used - Pure ACF/PACF analysis")

        self.arima_order = (p, d, q)
        self.seasonal_order = (P, D, Q, s)

        print(f"✓ Selected ARIMA order: {self.arima_order}")
        print(f"✓ Selected seasonal order: {self.seasonal_order}")
        print(f"✓ Based on: ACF/PACF patterns and domain knowledge")

        return self.arima_order, self.seasonal_order

    def train_models(self):
        """Train ARIMA and Holt-Winter models with improved methodology"""
        print("\n🤖 Training models with improved methodology...")

        # Test multiple SARIMAX configurations and select best using VALIDATION data
        print("Testing multiple SARIMAX configurations using validation data for selection...")

        # Test configurations based on ACF/PACF analysis
        test_configs = [
            ((2, 0, 1), (1, 0, 1, 24)),  # Conservative approach (from ACF/PACF)
            ((2, 0, 2), (1, 0, 1, 24)),  # Slightly more complex MA
            ((1, 0, 1), (1, 0, 1, 24)),  # Simple configuration
            ((3, 0, 1), (1, 0, 1, 24)),  # More AR terms
            ((2, 0, 1), (2, 0, 1, 24)),  # More seasonal AR
            ((1, 0, 2), (1, 0, 1, 24)),  # More MA terms
        ]

        best_validation_mae = float('inf')
        best_model = None
        best_config = None

        print("Training on 60% data, evaluating on validation data (20%)...")

        for order, seasonal_order in test_configs:
            try:
                print(f"  Testing SARIMAX{order}x{seasonal_order}...")

                # Train on training data
                model = SARIMAX(
                    self.train_data['Demand'],
                    exog=self.train_exog,
                    order=order,
                    seasonal_order=seasonal_order,
                    enforce_stationarity=False,
                    enforce_invertibility=False
                )
                fitted = model.fit(disp=False, maxiter=200)

                # Evaluate on validation data
                validation_forecast = fitted.get_forecast(
                    steps=len(self.validation_data),
                    exog=self.validation_exog
                )
                validation_pred = validation_forecast.predicted_mean
                validation_mae = mean_absolute_error(self.validation_data['Demand'], validation_pred)

                print(f"    AIC: {fitted.aic:.2f}, Validation MAE: {validation_mae:.2f}")

                if validation_mae < best_validation_mae:
                    best_validation_mae = validation_mae
                    best_model = fitted
                    best_config = (order, seasonal_order)
                    print(f"    ✓ New best model - Validation MAE: {validation_mae:.2f}")

            except Exception as e:
                print(f"    ✗ Failed: {str(e)[:50]}...")
                continue

        if best_model is not None:
            self.arima_fitted = best_model
            self.arima_order, self.seasonal_order = best_config
            print(f"✓ Best SARIMAX model selected: {self.arima_order}x{self.seasonal_order}")
            print(f"  Best Validation MAE: {best_validation_mae:.2f}")
            print(f"  Model AIC: {best_model.aic:.2f}")
        else:
            # Fallback to simple ARIMA
            print("All SARIMAX models failed, using simple ARIMA...")
            self.arima_model = ARIMA(self.train_data['Demand'], order=(2, 0, 1))
            self.arima_fitted = self.arima_model.fit()
            print("✓ Simple ARIMA fallback trained")
        
        # Improved Holt-Winter training with better data preprocessing
        print("\nImproved Holt-Winter training...")

        # Improved preprocessing for Holt-Winter (NO LOG TRANSFORMATION)
        demand_data = self.train_data['Demand'].copy()

        # Handle outliers more conservatively
        Q1 = demand_data.quantile(0.25)
        Q3 = demand_data.quantile(0.75)
        IQR = Q3 - Q1
        lower_bound = Q1 - 1.5 * IQR
        upper_bound = Q3 + 1.5 * IQR
        demand_data = demand_data.clip(lower=max(lower_bound, demand_data.quantile(0.05)),
                                      upper=min(upper_bound, demand_data.quantile(0.95)))

        # Ensure positive values for multiplicative models
        min_val = demand_data.min()
        if min_val <= 0:
            adjustment = abs(min_val) + 100  # Adjustment for stability
            demand_data = demand_data + adjustment
            print(f"  Adjusted data to ensure positive values (added {adjustment})")
        else:
            adjustment = 0

        print(f"  Using original data (NO log transformation) to correctly identify trend and seasonality")

        # Test multiple Holt-Winter configurations on original data
        hw_configs = [
            # (trend, seasonal, damped)
            ('add', 'add', False),      # Additive trend and seasonality
            ('add', 'mul', False),      # Additive trend, multiplicative seasonality
            ('mul', 'add', False),      # Multiplicative trend, additive seasonality
            ('mul', 'mul', False),      # Multiplicative trend and seasonality
            (None, 'add', False),       # No trend, additive seasonality
            (None, 'mul', False),       # No trend, multiplicative seasonality
            ('add', 'add', True),       # Damped additive trend, additive seasonality
            ('add', 'mul', True),       # Damped additive trend, multiplicative seasonality
        ]

        best_hw_aic = float('inf')
        best_hw_model = None
        best_hw_config = None

        for trend, seasonal, damped in hw_configs:
            try:
                config_name = f"trend={trend}, seasonal={seasonal}, damped={damped}"
                print(f"  Testing Holt-Winter ({config_name})...")

                model = ExponentialSmoothing(
                    demand_data,  # Always use original data
                    trend=trend,
                    seasonal=seasonal,
                    seasonal_periods=24,
                    damped_trend=damped,
                    initialization_method='estimated'
                )

                # Fit with robust parameters
                fitted = model.fit(
                    optimized=True,
                    remove_bias=True,
                    use_brute=True  # More robust optimization
                )

                if fitted.aic < best_hw_aic:
                    best_hw_aic = fitted.aic
                    best_hw_model = fitted
                    best_hw_config = (trend, seasonal, damped)
                    print(f"    ✓ New best H-W model - AIC: {fitted.aic:.2f}")
                else:
                    print(f"    AIC: {fitted.aic:.2f}")

            except Exception as e:
                print(f"    ✗ Failed: {str(e)[:50]}...")
                continue

        if best_hw_model is not None:
            self.hw_fitted = best_hw_model
            self.hw_data_adjustment = adjustment
            trend, seasonal, damped = best_hw_config
            self.hw_data_type = 'original'  # Always using original data now
            print(f"✓ Best Holt-Winter model selected:")
            print(f"  Configuration: trend={trend}, seasonal={seasonal}, damped={damped}")
            print(f"  Final AIC: {best_hw_aic:.2f}")
            print(f"  Data adjustment: {self.hw_data_adjustment}")
            print(f"  Using original data (no log transformation)")
        else:
            print("All advanced Holt-Winter models failed, using robust fallback...")
            try:
                # Simple but robust fallback
                self.hw_model = ExponentialSmoothing(
                    demand_data,
                    seasonal='add',
                    seasonal_periods=24
                )
                self.hw_fitted = self.hw_model.fit()
                self.hw_data_adjustment = adjustment
                self.hw_data_type = 'original'
                print("✓ Robust Holt-Winter fallback trained")
            except Exception as e:
                print(f"✗ All Holt-Winter models failed: {e}")
                self.hw_fitted = None
                self.hw_data_adjustment = 0
                self.hw_data_type = 'original'
    
    def generate_step_predictions(self):
        """Generate predictions using test data for final evaluation"""
        print("\n📈 Generating step-by-step predictions for final evaluation...")

        # Define the steps for daily and weekly forecasting
        daily_steps = [1, 8, 16, 24]  # 1h, 8h, 16h, 24h
        weekly_steps = [24, 48, 96, 168]  # 1d, 2d, 4d, 7d

        # Use TEST data for final evaluation (after model selection on validation)
        eval_data = self.test_data
        eval_exog = self.test_exog

        # Generate predictions for a reasonable number of time points
        num_forecasts = min(150, len(eval_data) - 168)  # Ensure we have enough data

        print(f"Generating {num_forecasts} rolling window forecasts")
        print("Using TEST data for final performance evaluation")
        print("(Models were selected using validation data)")

        # Initialize prediction storage
        self.daily_predictions = {
            'arima': {step: [] for step in daily_steps},
            'holt_winter': {step: [] for step in daily_steps},
            'actual': {step: [] for step in daily_steps}
        }

        self.weekly_predictions = {
            'arima': {step: [] for step in weekly_steps},
            'holt_winter': {step: [] for step in weekly_steps},
            'actual': {step: [] for step in weekly_steps}
        }

        # Rolling window forecasting
        for i in range(num_forecasts):
            if i % 30 == 0:
                print(f"  Progress: {i}/{num_forecasts}")

            # For each forecast origin, predict multiple steps ahead
            forecast_origin = i

            # Daily forecasting steps
            for step in daily_steps:
                if forecast_origin + step - 1 >= len(eval_data):
                    continue

                # Get actual value at forecast_origin + step
                actual_val = eval_data['Demand'].iloc[forecast_origin + step - 1]
                self.daily_predictions['actual'][step].append(actual_val)

                # ARIMA multi-step forecast
                try:
                    if hasattr(self.arima_fitted, 'get_forecast'):
                        # Use exogenous variables for multi-step forecast
                        if forecast_origin + step <= len(eval_exog):
                            exog_future = eval_exog.iloc[forecast_origin:forecast_origin + step]
                            forecast_result = self.arima_fitted.get_forecast(steps=step, exog=exog_future)
                            arima_pred = forecast_result.predicted_mean.iloc[-1]
                        else:
                            # Fallback without exogenous variables
                            arima_pred = self.arima_fitted.forecast(steps=step)[-1]
                    else:
                        arima_pred = self.arima_fitted.forecast(steps=step)[-1]

                    # Ensure prediction is reasonable
                    if np.isnan(arima_pred) or np.isinf(arima_pred):
                        arima_pred = self.train_data['Demand'].mean()

                except Exception as e:
                    arima_pred = self.train_data['Demand'].mean()

                self.daily_predictions['arima'][step].append(arima_pred)

                # Holt-Winter multi-step forecast (original data only)
                try:
                    if self.hw_fitted is not None:
                        hw_pred = self.hw_fitted.forecast(steps=step)[-1]

                        # Adjust back if we added offset for positive values
                        if hasattr(self, 'hw_data_adjustment') and self.hw_data_adjustment > 0:
                            hw_pred = hw_pred - self.hw_data_adjustment

                        # Ensure prediction is reasonable
                        if np.isnan(hw_pred) or np.isinf(hw_pred) or hw_pred < 0:
                            hw_pred = self.train_data['Demand'].mean()
                    else:
                        hw_pred = self.train_data['Demand'].mean()

                except Exception:
                    hw_pred = self.train_data['Demand'].mean()

                self.daily_predictions['holt_winter'][step].append(hw_pred)

            # Weekly forecasting steps
            for step in weekly_steps:
                if forecast_origin + step - 1 >= len(eval_data):
                    continue

                # Get actual value at forecast_origin + step
                actual_val = eval_data['Demand'].iloc[forecast_origin + step - 1]
                self.weekly_predictions['actual'][step].append(actual_val)

                # ARIMA multi-step forecast
                try:
                    if hasattr(self.arima_fitted, 'get_forecast'):
                        if forecast_origin + step <= len(eval_exog):
                            exog_future = eval_exog.iloc[forecast_origin:forecast_origin + step]
                            forecast_result = self.arima_fitted.get_forecast(steps=step, exog=exog_future)
                            arima_pred = forecast_result.predicted_mean.iloc[-1]
                        else:
                            arima_pred = self.arima_fitted.forecast(steps=step)[-1]
                    else:
                        arima_pred = self.arima_fitted.forecast(steps=step)[-1]

                    if np.isnan(arima_pred) or np.isinf(arima_pred):
                        arima_pred = self.train_data['Demand'].mean()

                except Exception as e:
                    arima_pred = self.train_data['Demand'].mean()

                self.weekly_predictions['arima'][step].append(arima_pred)

                # Holt-Winter multi-step forecast (original data only)
                try:
                    if self.hw_fitted is not None:
                        hw_pred = self.hw_fitted.forecast(steps=step)[-1]

                        # Adjust back if we added offset for positive values
                        if hasattr(self, 'hw_data_adjustment') and self.hw_data_adjustment > 0:
                            hw_pred = hw_pred - self.hw_data_adjustment

                        if np.isnan(hw_pred) or np.isinf(hw_pred) or hw_pred < 0:
                            hw_pred = self.train_data['Demand'].mean()
                    else:
                        hw_pred = self.train_data['Demand'].mean()

                except Exception:
                    hw_pred = self.train_data['Demand'].mean()

                self.weekly_predictions['holt_winter'][step].append(hw_pred)

        print("✓ Rolling window predictions generated with improved methodology")
    
    def create_forecasting_charts(self):
        """Create 4 charts as requested: ARIMA daily, ARIMA weekly, Holt-Winter daily, Holt-Winter weekly"""
        print("\n🎨 Creating 4 forecasting performance charts...")

        # Get time indices for visualization (use first 100 points for clarity)
        num_points = min(100, len(self.daily_predictions['actual'][1]))
        time_range = range(num_points)

        # Chart 1: SARIMAX Daily Forecasting (t+1, t+8, t+16, t+24) with Custom Colors
        plt.figure(figsize=(15, 7))

        # Plot actual values (same for all steps, just use t+1)
        actual_daily = self.daily_predictions['actual'][1][:num_points]
        plt.plot(time_range, actual_daily, 'black', label='Actual Demand', linewidth=3, alpha=0.9)

        # Plot SARIMAX predictions with specific custom colors
        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']  # Custom attractive colors
        line_styles = ['-', '--', '-.', ':']  # Different line styles for clarity
        steps = [1, 8, 16, 24]

        for i, step in enumerate(steps):
            pred_values = self.daily_predictions['arima'][step][:num_points]
            plt.plot(time_range, pred_values, color=colors[i],
                    label=f'SARIMAX t+{step}', linewidth=2.5, alpha=0.85,
                    linestyle=line_styles[i])

        plt.title('SARIMAX Daily Forecasting Performance (t+1, t+8, t+16, t+24)',
                 fontsize=16, fontweight='bold', color='#2C3E50')
        plt.xlabel('Time Points', fontsize=12, fontweight='bold', color='#34495E')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold', color='#34495E')
        plt.legend(fontsize=11, loc='upper right', framealpha=0.9)
        plt.grid(True, alpha=0.3, color='gray', linestyle='-', linewidth=0.5)
        plt.tight_layout()
        plt.savefig('sarimax_daily_forecasting.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Chart 2: SARIMAX Weekly Forecasting (t+24, t+48, t+96, t+168)
        plt.figure(figsize=(15, 7))

        # Plot actual values
        actual_weekly = self.weekly_predictions['actual'][24][:num_points]
        plt.plot(time_range, actual_weekly, 'black', label='Actual Demand', linewidth=3, alpha=0.9)

        # Plot SARIMAX predictions for each step
        colors = ['#E74C3C', '#3498DB', '#9B59B6', '#F39C12']  # Different color scheme for weekly
        line_styles = ['-', '--', '-.', ':']
        steps = [24, 48, 96, 168]
        for i, step in enumerate(steps):
            pred_values = self.weekly_predictions['arima'][step][:num_points]
            plt.plot(time_range, pred_values, color=colors[i],
                    label=f'SARIMAX t+{step}', linewidth=2.5, alpha=0.85,
                    linestyle=line_styles[i])

        plt.title('SARIMAX Weekly Forecasting Performance (t+24, t+48, t+96, t+168)',
                 fontsize=16, fontweight='bold', color='#2C3E50')
        plt.xlabel('Time Points', fontsize=12, fontweight='bold', color='#34495E')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold', color='#34495E')
        plt.legend(fontsize=11, loc='upper right', framealpha=0.9)
        plt.grid(True, alpha=0.3, color='gray', linestyle='-', linewidth=0.5)
        plt.tight_layout()
        plt.savefig('sarimax_weekly_forecasting.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Chart 3: Holt-Winter Daily Forecasting (t+1, t+8, t+16, t+24)
        plt.figure(figsize=(15, 7))

        # Plot actual values
        plt.plot(time_range, actual_daily, 'k-', label='Actual Demand', linewidth=3, alpha=0.9)

        # Plot Holt-Winter predictions for each step
        colors = ['red', 'orange', 'darkred', 'maroon']
        steps = [1, 8, 16, 24]
        for i, step in enumerate(steps):
            pred_values = self.daily_predictions['holt_winter'][step][:num_points]
            plt.plot(time_range, pred_values, color=colors[i],
                    label=f'Holt-Winter t+{step}', linewidth=2, alpha=0.8, linestyle='--')

        plt.title('Holt-Winter Daily Forecasting Performance (t+1, t+8, t+16, t+24)',
                 fontsize=16, fontweight='bold')
        plt.xlabel('Time Points', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=11, loc='upper right')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('holt_winter_daily_forecasting.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Chart 4: Holt-Winter Weekly Forecasting (t+24, t+48, t+96, t+168)
        plt.figure(figsize=(15, 7))

        # Plot actual values
        plt.plot(time_range, actual_weekly, 'k-', label='Actual Demand', linewidth=3, alpha=0.9)

        # Plot Holt-Winter predictions for each step
        colors = ['red', 'orange', 'darkred', 'maroon']
        steps = [24, 48, 96, 168]
        for i, step in enumerate(steps):
            pred_values = self.weekly_predictions['holt_winter'][step][:num_points]
            plt.plot(time_range, pred_values, color=colors[i],
                    label=f'Holt-Winter t+{step}', linewidth=2, alpha=0.8, linestyle='--')

        plt.title('Holt-Winter Weekly Forecasting Performance (t+24, t+48, t+96, t+168)',
                 fontsize=16, fontweight='bold')
        plt.xlabel('Time Points', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=11, loc='upper right')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('holt_winter_weekly_forecasting.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ All 4 forecasting charts created and saved")
    
    def calculate_performance_metrics(self):
        """Calculate comprehensive performance metrics including MAPE"""
        print("\n📊 COMPREHENSIVE PERFORMANCE METRICS:")

        print("\n🔹 DAILY FORECASTING PERFORMANCE:")
        print(f"{'Step':<8} {'ARIMA MAE':<12} {'ARIMA MAPE%':<13} {'H-W MAE':<12} {'H-W MAPE%':<13} {'Winner':<15}")
        print("-" * 85)

        daily_steps = [1, 8, 16, 24]
        for step in daily_steps:
            actual_vals = self.daily_predictions['actual'][step]
            arima_vals = self.daily_predictions['arima'][step]
            hw_vals = self.daily_predictions['holt_winter'][step]

            # Calculate MAE
            arima_mae = mean_absolute_error(actual_vals, arima_vals)
            hw_mae = mean_absolute_error(actual_vals, hw_vals)

            # Calculate MAPE (handle division by zero)
            try:
                arima_mape = mean_absolute_percentage_error(actual_vals, arima_vals) * 100
            except:
                arima_mape = float('inf')

            try:
                hw_mape = mean_absolute_percentage_error(actual_vals, hw_vals) * 100
            except:
                hw_mape = float('inf')

            # Determine winner based on MAE
            winner = "Holt-Winter" if hw_mae < arima_mae else "ARIMA"

            print(f"t+{step:<6} {arima_mae:<12.2f} {arima_mape:<13.2f} {hw_mae:<12.2f} {hw_mape:<13.2f} {winner:<15}")

        print("\n🔹 WEEKLY FORECASTING PERFORMANCE:")
        print(f"{'Step':<8} {'ARIMA MAE':<12} {'ARIMA MAPE%':<13} {'H-W MAE':<12} {'H-W MAPE%':<13} {'Winner':<15}")
        print("-" * 85)

        weekly_steps = [24, 48, 96, 168]
        for step in weekly_steps:
            actual_vals = self.weekly_predictions['actual'][step]
            arima_vals = self.weekly_predictions['arima'][step]
            hw_vals = self.weekly_predictions['holt_winter'][step]

            # Calculate MAE
            arima_mae = mean_absolute_error(actual_vals, arima_vals)
            hw_mae = mean_absolute_error(actual_vals, hw_vals)

            # Calculate MAPE
            try:
                arima_mape = mean_absolute_percentage_error(actual_vals, arima_vals) * 100
            except:
                arima_mape = float('inf')

            try:
                hw_mape = mean_absolute_percentage_error(actual_vals, hw_vals) * 100
            except:
                hw_mape = float('inf')

            # Determine winner based on MAE
            winner = "Holt-Winter" if hw_mae < arima_mae else "ARIMA"

            print(f"t+{step:<6} {arima_mae:<12.2f} {arima_mape:<13.2f} {hw_mae:<12.2f} {hw_mape:<13.2f} {winner:<15}")

        # Summary statistics
        print("\n🔹 OVERALL PERFORMANCE SUMMARY:")
        all_daily_arima_mae = []
        all_daily_hw_mae = []
        all_weekly_arima_mae = []
        all_weekly_hw_mae = []

        for step in daily_steps:
            actual_vals = self.daily_predictions['actual'][step]
            arima_vals = self.daily_predictions['arima'][step]
            hw_vals = self.daily_predictions['holt_winter'][step]
            all_daily_arima_mae.append(mean_absolute_error(actual_vals, arima_vals))
            all_daily_hw_mae.append(mean_absolute_error(actual_vals, hw_vals))

        for step in weekly_steps:
            actual_vals = self.weekly_predictions['actual'][step]
            arima_vals = self.weekly_predictions['arima'][step]
            hw_vals = self.weekly_predictions['holt_winter'][step]
            all_weekly_arima_mae.append(mean_absolute_error(actual_vals, arima_vals))
            all_weekly_hw_mae.append(mean_absolute_error(actual_vals, hw_vals))

        print(f"Daily Average MAE  - ARIMA: {np.mean(all_daily_arima_mae):.2f}, Holt-Winter: {np.mean(all_daily_hw_mae):.2f}")
        print(f"Weekly Average MAE - ARIMA: {np.mean(all_weekly_arima_mae):.2f}, Holt-Winter: {np.mean(all_weekly_hw_mae):.2f}")

        arima_wins = sum(1 for i in range(len(all_daily_arima_mae)) if all_daily_arima_mae[i] < all_daily_hw_mae[i])
        arima_wins += sum(1 for i in range(len(all_weekly_arima_mae)) if all_weekly_arima_mae[i] < all_weekly_hw_mae[i])

        print(f"ARIMA wins: {arima_wins}/8 forecasting horizons ({arima_wins/8*100:.1f}%)")
    
    def run_complete_analysis(self):
        """Run the complete individual step analysis"""
        print("🔍 STARTING INDIVIDUAL STEP FORECASTING ANALYSIS")
        print("Creating separate charts for t+1, t+6, t+12, t+18, t+24")
        print("="*80)
        
        try:
            # Step 1: Load and prepare data
            self.load_and_prepare_data()

            # Step 2: Perform ACF/PACF analysis for manual parameter selection
            self.perform_acf_pacf_analysis()

            # Step 3: Train models with manually selected parameters
            self.train_models()

            # Step 4: Generate step predictions
            self.generate_step_predictions()

            # Step 5: Create forecasting charts
            self.create_forecasting_charts()

            # Step 6: Calculate performance metrics
            self.calculate_performance_metrics()

            print(f"\n✅ ENHANCED SARIMAX & HOLT-WINTER ANALYSIS COMPLETED!")
            print(f"📁 Generated 4 forecasting performance charts:")
            print(f"   • sarimax_daily_forecasting.png (with custom colors)")
            print(f"   • sarimax_weekly_forecasting.png (with custom colors)")
            print(f"   • holt_winter_daily_forecasting.png (improved)")
            print(f"   • holt_winter_weekly_forecasting.png (improved)")
            print(f"📁 Generated ACF/PACF analysis:")
            print(f"   • acf_pacf_analysis.png")
            print(f"🎨 Features: Custom colors, SARIMAX labeling, improved Holt-Winter")
            
            return True
            
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function"""
    print("🔍 INDIVIDUAL STEP FORECASTING ANALYSIS")
    print("Separate Charts for Each Forecasting Step")
    print("="*80)
    
    # Initialize analyzer
    analyzer = IndividualStepForecasting('electric_demand_1h.csv')
    
    # Run complete analysis
    success = analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
