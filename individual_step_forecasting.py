"""
Individual Step Forecasting Analysis
Creates separate charts for each forecasting step: t+1, t+6, t+12, t+18, t+24

Each chart shows:
- Actual Demand line
- ARIMA predictions for that specific step
- Holt-Winter predictions for that specific step
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Time Series Libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_absolute_error
from sklearn.preprocessing import StandardScaler

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class IndividualStepForecasting:
    def __init__(self, data_path):
        """Initialize the individual step forecasting analyzer"""
        self.data_path = data_path
        self.data = None
        self.train_data = None
        self.test_data = None
        self.arima_fitted = None
        self.hw_fitted = None
        self.arima_order = None
        self.seasonal_order = None
        self.scaler = StandardScaler()
        
        # Store predictions for each step
        self.predictions_t1 = {'arima': [], 'holt_winter': []}
        self.predictions_t6 = {'arima': [], 'holt_winter': []}
        self.predictions_t12 = {'arima': [], 'holt_winter': []}
        self.predictions_t18 = {'arima': [], 'holt_winter': []}
        self.predictions_t24 = {'arima': [], 'holt_winter': []}
        
    def load_and_prepare_data(self):
        """Load and prepare data"""
        print("🔍 INDIVIDUAL STEP FORECASTING ANALYSIS")
        print("Separate charts for t+1, t+6, t+12, t+18, t+24")
        print("="*60)
        print("📊 Loading data...")
        
        # Load data
        self.data = pd.read_csv(self.data_path)
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        self.data = self.data.asfreq('H')  # Set hourly frequency
        
        print(f"Data shape: {self.data.shape}")
        print(f"Date range: {self.data.index.min()} to {self.data.index.max()}")
        
        # Enhanced feature engineering for SARIMAX
        print("🔧 Creating exogenous variables...")
        
        # Cyclical time features
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data.index.hour / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data.index.hour / 24)
        self.data['day_sin'] = np.sin(2 * np.pi * self.data.index.dayofweek / 7)
        self.data['day_cos'] = np.cos(2 * np.pi * self.data.index.dayofweek / 7)
        
        # Scale temperature
        temp_scaled = self.scaler.fit_transform(self.data[['Temperature']])
        self.data['Temperature_scaled'] = temp_scaled.flatten()
        
        # Exogenous variables for SARIMAX
        self.exog_cols = ['Temperature_scaled', 'Holiday', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos']
        self.exog_data = self.data[self.exog_cols].copy()
        
        # Split data: 60% training, 20% testing, 20% validation
        total_len = len(self.data)
        train_end = int(0.6 * total_len)
        test_end = int(0.8 * total_len)

        self.train_data = self.data.iloc[:train_end].copy()
        self.test_data = self.data.iloc[train_end:test_end].copy()
        self.validation_data = self.data.iloc[test_end:].copy()

        self.train_exog = self.exog_data.iloc[:train_end].copy()
        self.test_exog = self.exog_data.iloc[train_end:test_end].copy()
        self.validation_exog = self.exog_data.iloc[test_end:].copy()

        print(f"Training: {len(self.train_data)} samples (60%)")
        print(f"Testing: {len(self.test_data)} samples (20%)")
        print(f"Validation: {len(self.validation_data)} samples (20%)")
        
        return self.data

    def perform_acf_pacf_analysis(self):
        """Perform ACF and PACF analysis to determine ARIMA parameters manually"""
        print("\n📈 ACF/PACF Analysis for Manual ARIMA Parameter Selection...")

        # Check stationarity first
        adf_result = adfuller(self.train_data['Demand'].dropna())
        print(f"\nStationarity Test (ADF):")
        print(f"ADF Statistic: {adf_result[0]:.6f}")
        print(f"p-value: {adf_result[1]:.6f}")

        is_stationary = adf_result[1] <= 0.05
        print(f"Series is {'stationary' if is_stationary else 'non-stationary'}")

        # Determine differencing order (d)
        d = 0 if is_stationary else 1

        # Prepare series for ACF/PACF analysis
        if d == 1:
            diff_series = self.train_data['Demand'].diff().dropna()
            print("Using first-differenced series for ACF/PACF analysis")
        else:
            diff_series = self.train_data['Demand'].dropna()
            print("Using original series for ACF/PACF analysis")

        # Create ACF and PACF plots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # Original series
        axes[0,0].plot(self.train_data['Demand'])
        axes[0,0].set_title('Original Demand Series', fontsize=14, fontweight='bold')
        axes[0,0].set_ylabel('Demand (kW)')
        axes[0,0].grid(True, alpha=0.3)

        # Differenced series (if applicable)
        if d == 1:
            axes[0,1].plot(diff_series)
            axes[0,1].set_title('First-Differenced Series', fontsize=14, fontweight='bold')
            axes[0,1].set_ylabel('Differenced Demand')
            axes[0,1].grid(True, alpha=0.3)
        else:
            axes[0,1].plot(self.train_data['Demand'])
            axes[0,1].set_title('Stationary Series', fontsize=14, fontweight='bold')
            axes[0,1].set_ylabel('Demand (kW)')
            axes[0,1].grid(True, alpha=0.3)

        # ACF plot
        plot_acf(diff_series, lags=40, ax=axes[1,0], alpha=0.05)
        axes[1,0].set_title('Autocorrelation Function (ACF)', fontsize=14, fontweight='bold')
        axes[1,0].grid(True, alpha=0.3)

        # PACF plot
        plot_pacf(diff_series, lags=40, ax=axes[1,1], alpha=0.05)
        axes[1,1].set_title('Partial Autocorrelation Function (PACF)', fontsize=14, fontweight='bold')
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('acf_pacf_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Manual parameter selection based on ACF/PACF analysis
        print("\n🎯 Manual ARIMA Parameter Selection Based on ACF/PACF:")
        print("Analyzing ACF and PACF patterns from your plots...")

        # Based on your ACF/PACF plots:
        # ACF: Strong seasonal pattern with peaks every ~24 lags, slow decay
        # PACF: Significant spikes at lags 1-3, then cuts off

        # Non-seasonal parameters
        p = 3  # AR order - PACF shows significant spikes at lags 1, 2, 3
        d = d  # Differencing order from stationarity test (0 since stationary)
        q = 1  # MA order - ACF shows gradual decay, suggesting MA(1)

        # Seasonal parameters for 24-hour cycle
        # ACF shows strong seasonal pattern every 24 lags
        P = 1  # Seasonal AR - one significant seasonal spike in PACF
        D = 1  # Seasonal differencing - to handle seasonal trend
        Q = 1  # Seasonal MA - seasonal pattern in ACF
        s = 24 # Seasonal period (24 hours)

        self.arima_order = (p, d, q)
        self.seasonal_order = (P, D, Q, s)

        print(f"✓ Selected ARIMA order: {self.arima_order}")
        print(f"✓ Selected seasonal order: {self.seasonal_order}")
        print(f"✓ Based on: ACF/PACF patterns and domain knowledge")

        return self.arima_order, self.seasonal_order

    def train_models(self):
        """Train ARIMA and Holt-Winter models with manually selected parameters"""
        print("\n🤖 Training models with manually selected parameters...")

        # Train SARIMAX/ARIMA model with manual parameters
        print(f"Training SARIMAX with order={self.arima_order}, seasonal_order={self.seasonal_order}...")
        try:
            self.arima_model = SARIMAX(
                self.train_data['Demand'],
                exog=self.train_exog,
                order=self.arima_order,
                seasonal_order=self.seasonal_order,
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            self.arima_fitted = self.arima_model.fit(disp=False, maxiter=100)
            print(f"✓ SARIMAX trained with manual parameters - AIC: {self.arima_fitted.aic:.2f}")

        except Exception as e:
            print(f"SARIMAX failed: {e}, using simple ARIMA with manual order")
            self.arima_model = ARIMA(self.train_data['Demand'], order=self.arima_order)
            self.arima_fitted = self.arima_model.fit()
            print("✓ ARIMA fallback trained with manual parameters")
        
        # Train Holt-Winter model
        print("Training Holt-Winter...")
        try:
            self.hw_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24,
                initialization_method='estimated'
            )
            self.hw_fitted = self.hw_model.fit(optimized=True, remove_bias=True)
            print(f"✓ Holt-Winter trained - AIC: {self.hw_fitted.aic:.2f}")
            
        except Exception as e:
            print(f"Optimized Holt-Winter failed: {e}, using simple version")
            self.hw_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24
            )
            self.hw_fitted = self.hw_model.fit()
            print("✓ Simple Holt-Winter trained")
    
    def generate_step_predictions(self):
        """Generate predictions for specific steps: daily and weekly forecasting"""
        print("\n📈 Generating step-by-step predictions...")

        # Define the steps for daily and weekly forecasting
        daily_steps = [1, 8, 16, 24]  # 1h, 8h, 16h, 24h
        weekly_steps = [24, 48, 96, 168]  # 1d, 2d, 4d, 7d

        # Use validation data for final evaluation
        eval_data = self.validation_data
        eval_exog = self.validation_exog

        # Generate predictions for a reasonable number of time points
        num_forecasts = min(200, len(eval_data) - 168)  # Ensure we have enough data for 7-day forecasts

        print(f"Generating {num_forecasts} forecasts for daily and weekly horizons")

        # Initialize prediction storage
        self.daily_predictions = {
            'arima': {step: [] for step in daily_steps},
            'holt_winter': {step: [] for step in daily_steps},
            'actual': {step: [] for step in daily_steps}
        }

        self.weekly_predictions = {
            'arima': {step: [] for step in weekly_steps},
            'holt_winter': {step: [] for step in weekly_steps},
            'actual': {step: [] for step in weekly_steps}
        }

        # Generate predictions
        for i in range(num_forecasts):
            if i % 50 == 0:
                print(f"  Progress: {i}/{num_forecasts}")

            # Daily forecasting steps
            for step in daily_steps:
                if i + step - 1 >= len(eval_data):
                    continue

                # Get actual value
                actual_val = eval_data['Demand'].iloc[i + step - 1]
                self.daily_predictions['actual'][step].append(actual_val)

                # ARIMA prediction
                try:
                    if hasattr(self.arima_fitted, 'get_forecast') and step <= len(eval_exog) - i:
                        exog_step = eval_exog.iloc[i:i + step]
                        forecast_result = self.arima_fitted.get_forecast(steps=step, exog=exog_step)
                        arima_pred = forecast_result.predicted_mean.iloc[-1]
                    else:
                        arima_pred = self.arima_fitted.forecast(steps=step)[-1]
                except:
                    arima_pred = self.train_data['Demand'].mean()

                self.daily_predictions['arima'][step].append(arima_pred)

                # Holt-Winter prediction
                try:
                    hw_pred = self.hw_fitted.forecast(steps=step)[-1]
                except:
                    hw_pred = self.train_data['Demand'].mean()

                self.daily_predictions['holt_winter'][step].append(hw_pred)

            # Weekly forecasting steps
            for step in weekly_steps:
                if i + step - 1 >= len(eval_data):
                    continue

                # Get actual value
                actual_val = eval_data['Demand'].iloc[i + step - 1]
                self.weekly_predictions['actual'][step].append(actual_val)

                # ARIMA prediction
                try:
                    if hasattr(self.arima_fitted, 'get_forecast') and step <= len(eval_exog) - i:
                        exog_step = eval_exog.iloc[i:i + step]
                        forecast_result = self.arima_fitted.get_forecast(steps=step, exog=exog_step)
                        arima_pred = forecast_result.predicted_mean.iloc[-1]
                    else:
                        arima_pred = self.arima_fitted.forecast(steps=step)[-1]
                except:
                    arima_pred = self.train_data['Demand'].mean()

                self.weekly_predictions['arima'][step].append(arima_pred)

                # Holt-Winter prediction
                try:
                    hw_pred = self.hw_fitted.forecast(steps=step)[-1]
                except:
                    hw_pred = self.train_data['Demand'].mean()

                self.weekly_predictions['holt_winter'][step].append(hw_pred)

        print("✓ Daily and weekly step predictions generated")
    
    def create_forecasting_charts(self):
        """Create 4 charts as requested: ARIMA daily, ARIMA weekly, Holt-Winter daily, Holt-Winter weekly"""
        print("\n🎨 Creating 4 forecasting performance charts...")

        # Get time indices for visualization (use first 100 points for clarity)
        num_points = min(100, len(self.daily_predictions['actual'][1]))
        time_range = range(num_points)

        # Chart 1: ARIMA Daily Forecasting (t+1, t+8, t+16, t+24)
        plt.figure(figsize=(15, 7))

        # Plot actual values (same for all steps, just use t+1)
        actual_daily = self.daily_predictions['actual'][1][:num_points]
        plt.plot(time_range, actual_daily, 'k-', label='Actual Demand', linewidth=3, alpha=0.9)

        # Plot ARIMA predictions for each step
        colors = ['blue', 'cyan', 'navy', 'darkblue']
        steps = [1, 8, 16, 24]
        for i, step in enumerate(steps):
            pred_values = self.daily_predictions['arima'][step][:num_points]
            plt.plot(time_range, pred_values, color=colors[i],
                    label=f'ARIMA t+{step}', linewidth=2, alpha=0.8, linestyle='--')

        plt.title('ARIMA Daily Forecasting Performance (t+1, t+8, t+16, t+24)',
                 fontsize=16, fontweight='bold')
        plt.xlabel('Time Points', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=11, loc='upper right')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('arima_daily_forecasting.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Chart 2: ARIMA Weekly Forecasting (t+24, t+48, t+96, t+168)
        plt.figure(figsize=(15, 7))

        # Plot actual values
        actual_weekly = self.weekly_predictions['actual'][24][:num_points]
        plt.plot(time_range, actual_weekly, 'k-', label='Actual Demand', linewidth=3, alpha=0.9)

        # Plot ARIMA predictions for each step
        colors = ['blue', 'cyan', 'navy', 'darkblue']
        steps = [24, 48, 96, 168]
        for i, step in enumerate(steps):
            pred_values = self.weekly_predictions['arima'][step][:num_points]
            plt.plot(time_range, pred_values, color=colors[i],
                    label=f'ARIMA t+{step}', linewidth=2, alpha=0.8, linestyle='--')

        plt.title('ARIMA Weekly Forecasting Performance (t+24, t+48, t+96, t+168)',
                 fontsize=16, fontweight='bold')
        plt.xlabel('Time Points', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=11, loc='upper right')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('arima_weekly_forecasting.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Chart 3: Holt-Winter Daily Forecasting (t+1, t+8, t+16, t+24)
        plt.figure(figsize=(15, 7))

        # Plot actual values
        plt.plot(time_range, actual_daily, 'k-', label='Actual Demand', linewidth=3, alpha=0.9)

        # Plot Holt-Winter predictions for each step
        colors = ['red', 'orange', 'darkred', 'maroon']
        steps = [1, 8, 16, 24]
        for i, step in enumerate(steps):
            pred_values = self.daily_predictions['holt_winter'][step][:num_points]
            plt.plot(time_range, pred_values, color=colors[i],
                    label=f'Holt-Winter t+{step}', linewidth=2, alpha=0.8, linestyle='--')

        plt.title('Holt-Winter Daily Forecasting Performance (t+1, t+8, t+16, t+24)',
                 fontsize=16, fontweight='bold')
        plt.xlabel('Time Points', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=11, loc='upper right')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('holt_winter_daily_forecasting.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Chart 4: Holt-Winter Weekly Forecasting (t+24, t+48, t+96, t+168)
        plt.figure(figsize=(15, 7))

        # Plot actual values
        plt.plot(time_range, actual_weekly, 'k-', label='Actual Demand', linewidth=3, alpha=0.9)

        # Plot Holt-Winter predictions for each step
        colors = ['red', 'orange', 'darkred', 'maroon']
        steps = [24, 48, 96, 168]
        for i, step in enumerate(steps):
            pred_values = self.weekly_predictions['holt_winter'][step][:num_points]
            plt.plot(time_range, pred_values, color=colors[i],
                    label=f'Holt-Winter t+{step}', linewidth=2, alpha=0.8, linestyle='--')

        plt.title('Holt-Winter Weekly Forecasting Performance (t+24, t+48, t+96, t+168)',
                 fontsize=16, fontweight='bold')
        plt.xlabel('Time Points', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=11, loc='upper right')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig('holt_winter_weekly_forecasting.png', dpi=300, bbox_inches='tight')
        plt.show()

        print("✓ All 4 forecasting charts created and saved")
    
    def calculate_performance_metrics(self):
        """Calculate performance metrics for daily and weekly forecasting"""
        print("\n📊 Performance Metrics:")

        print("\n🔹 DAILY FORECASTING PERFORMANCE:")
        print(f"{'Step':<8} {'ARIMA MAE':<12} {'H-W MAE':<12} {'Winner':<15}")
        print("-" * 55)

        daily_steps = [1, 8, 16, 24]
        for step in daily_steps:
            actual_vals = self.daily_predictions['actual'][step]
            arima_vals = self.daily_predictions['arima'][step]
            hw_vals = self.daily_predictions['holt_winter'][step]

            arima_mae = mean_absolute_error(actual_vals, arima_vals)
            hw_mae = mean_absolute_error(actual_vals, hw_vals)

            winner = "Holt-Winter" if hw_mae < arima_mae else "ARIMA"

            print(f"t+{step:<6} {arima_mae:<12.2f} {hw_mae:<12.2f} {winner:<15}")

        print("\n🔹 WEEKLY FORECASTING PERFORMANCE:")
        print(f"{'Step':<8} {'ARIMA MAE':<12} {'H-W MAE':<12} {'Winner':<15}")
        print("-" * 55)

        weekly_steps = [24, 48, 96, 168]
        for step in weekly_steps:
            actual_vals = self.weekly_predictions['actual'][step]
            arima_vals = self.weekly_predictions['arima'][step]
            hw_vals = self.weekly_predictions['holt_winter'][step]

            arima_mae = mean_absolute_error(actual_vals, arima_vals)
            hw_mae = mean_absolute_error(actual_vals, hw_vals)

            winner = "Holt-Winter" if hw_mae < arima_mae else "ARIMA"

            print(f"t+{step:<6} {arima_mae:<12.2f} {hw_mae:<12.2f} {winner:<15}")
    
    def run_complete_analysis(self):
        """Run the complete individual step analysis"""
        print("🔍 STARTING INDIVIDUAL STEP FORECASTING ANALYSIS")
        print("Creating separate charts for t+1, t+6, t+12, t+18, t+24")
        print("="*80)
        
        try:
            # Step 1: Load and prepare data
            self.load_and_prepare_data()

            # Step 2: Perform ACF/PACF analysis for manual parameter selection
            self.perform_acf_pacf_analysis()

            # Step 3: Train models with manually selected parameters
            self.train_models()

            # Step 4: Generate step predictions
            self.generate_step_predictions()

            # Step 5: Create forecasting charts
            self.create_forecasting_charts()

            # Step 6: Calculate performance metrics
            self.calculate_performance_metrics()

            print(f"\n✅ IMPROVED FORECASTING ANALYSIS COMPLETED!")
            print(f"📁 Generated 4 forecasting performance charts:")
            print(f"   • arima_daily_forecasting.png")
            print(f"   • arima_weekly_forecasting.png")
            print(f"   • holt_winter_daily_forecasting.png")
            print(f"   • holt_winter_weekly_forecasting.png")
            print(f"📁 Generated ACF/PACF analysis:")
            print(f"   • acf_pacf_analysis.png")
            
            return True
            
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function"""
    print("🔍 INDIVIDUAL STEP FORECASTING ANALYSIS")
    print("Separate Charts for Each Forecasting Step")
    print("="*80)
    
    # Initialize analyzer
    analyzer = IndividualStepForecasting('electric_demand_1h.csv')
    
    # Run complete analysis
    success = analyzer.run_complete_analysis()


if __name__ == "__main__":
    main()
