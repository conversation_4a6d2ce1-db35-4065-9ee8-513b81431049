"""
Individual Step Forecasting Analysis
Creates separate charts for each forecasting step: t+1, t+6, t+12, t+18, t+24

Each chart shows:
- Actual Demand line
- ARIMA predictions for that specific step
- Holt-Winter predictions for that specific step
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Time Series Libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from sklearn.metrics import mean_absolute_error
from sklearn.preprocessing import StandardScaler

# Set style
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class IndividualStepForecasting:
    def __init__(self, data_path):
        """Initialize the individual step forecasting analyzer"""
        self.data_path = data_path
        self.data = None
        self.train_data = None
        self.test_data = None
        self.arima_fitted = None
        self.hw_fitted = None
        self.arima_order = None
        self.seasonal_order = None
        self.scaler = StandardScaler()
        
        # Store predictions for each step
        self.predictions_t1 = {'arima': [], 'holt_winter': []}
        self.predictions_t6 = {'arima': [], 'holt_winter': []}
        self.predictions_t12 = {'arima': [], 'holt_winter': []}
        self.predictions_t18 = {'arima': [], 'holt_winter': []}
        self.predictions_t24 = {'arima': [], 'holt_winter': []}
        
    def load_and_prepare_data(self):
        """Load and prepare data"""
        print("🔍 INDIVIDUAL STEP FORECASTING ANALYSIS")
        print("Separate charts for t+1, t+6, t+12, t+18, t+24")
        print("="*60)
        print("📊 Loading data...")
        
        # Load data
        self.data = pd.read_csv(self.data_path)
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        self.data = self.data.asfreq('H')  # Set hourly frequency
        
        print(f"Data shape: {self.data.shape}")
        print(f"Date range: {self.data.index.min()} to {self.data.index.max()}")
        
        # Enhanced feature engineering for SARIMAX
        print("🔧 Creating exogenous variables...")
        
        # Cyclical time features
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data.index.hour / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data.index.hour / 24)
        self.data['day_sin'] = np.sin(2 * np.pi * self.data.index.dayofweek / 7)
        self.data['day_cos'] = np.cos(2 * np.pi * self.data.index.dayofweek / 7)
        
        # Scale temperature
        temp_scaled = self.scaler.fit_transform(self.data[['Temperature']])
        self.data['Temperature_scaled'] = temp_scaled.flatten()
        
        # Exogenous variables for SARIMAX
        self.exog_cols = ['Temperature_scaled', 'Holiday', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos']
        self.exog_data = self.data[self.exog_cols].copy()
        
        # Split data (use 80% for training, 20% for testing)
        split_idx = int(len(self.data) * 0.8)
        self.train_data = self.data.iloc[:split_idx].copy()
        self.test_data = self.data.iloc[split_idx:].copy()
        
        self.train_exog = self.exog_data.iloc[:split_idx].copy()
        self.test_exog = self.exog_data.iloc[split_idx:].copy()
        
        print(f"Training: {len(self.train_data)} samples")
        print(f"Test: {len(self.test_data)} samples")
        
        return self.data

    def perform_acf_pacf_analysis(self):
        """Perform ACF and PACF analysis to determine ARIMA parameters manually"""
        print("\n📈 ACF/PACF Analysis for Manual ARIMA Parameter Selection...")

        # Check stationarity first
        adf_result = adfuller(self.train_data['Demand'].dropna())
        print(f"\nStationarity Test (ADF):")
        print(f"ADF Statistic: {adf_result[0]:.6f}")
        print(f"p-value: {adf_result[1]:.6f}")

        is_stationary = adf_result[1] <= 0.05
        print(f"Series is {'stationary' if is_stationary else 'non-stationary'}")

        # Determine differencing order (d)
        d = 0 if is_stationary else 1

        # Prepare series for ACF/PACF analysis
        if d == 1:
            diff_series = self.train_data['Demand'].diff().dropna()
            print("Using first-differenced series for ACF/PACF analysis")
        else:
            diff_series = self.train_data['Demand'].dropna()
            print("Using original series for ACF/PACF analysis")

        # Create ACF and PACF plots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # Original series
        axes[0,0].plot(self.train_data['Demand'])
        axes[0,0].set_title('Original Demand Series', fontsize=14, fontweight='bold')
        axes[0,0].set_ylabel('Demand (kW)')
        axes[0,0].grid(True, alpha=0.3)

        # Differenced series (if applicable)
        if d == 1:
            axes[0,1].plot(diff_series)
            axes[0,1].set_title('First-Differenced Series', fontsize=14, fontweight='bold')
            axes[0,1].set_ylabel('Differenced Demand')
            axes[0,1].grid(True, alpha=0.3)
        else:
            axes[0,1].plot(self.train_data['Demand'])
            axes[0,1].set_title('Stationary Series', fontsize=14, fontweight='bold')
            axes[0,1].set_ylabel('Demand (kW)')
            axes[0,1].grid(True, alpha=0.3)

        # ACF plot
        plot_acf(diff_series, lags=40, ax=axes[1,0], alpha=0.05)
        axes[1,0].set_title('Autocorrelation Function (ACF)', fontsize=14, fontweight='bold')
        axes[1,0].grid(True, alpha=0.3)

        # PACF plot
        plot_pacf(diff_series, lags=40, ax=axes[1,1], alpha=0.05)
        axes[1,1].set_title('Partial Autocorrelation Function (PACF)', fontsize=14, fontweight='bold')
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('acf_pacf_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Manual parameter selection based on ACF/PACF analysis
        print("\n🎯 Manual ARIMA Parameter Selection Based on ACF/PACF:")
        print("Analyzing ACF and PACF patterns...")

        # For electric demand data with daily seasonality, select parameters based on patterns:
        # - PACF helps determine AR order (p)
        # - ACF helps determine MA order (q)
        # - Differencing order (d) from stationarity test

        p = 2  # AR order - typically 1-3 for demand data
        d = d  # Differencing order from stationarity test
        q = 2  # MA order - typically 1-3 for demand data

        # Seasonal parameters for 24-hour cycle
        P = 1  # Seasonal AR - usually 1 for daily patterns
        D = 1  # Seasonal differencing - usually 1
        Q = 1  # Seasonal MA - usually 1
        s = 24 # Seasonal period (24 hours)

        self.arima_order = (p, d, q)
        self.seasonal_order = (P, D, Q, s)

        print(f"✓ Selected ARIMA order: {self.arima_order}")
        print(f"✓ Selected seasonal order: {self.seasonal_order}")
        print(f"✓ Based on: ACF/PACF patterns and domain knowledge")

        return self.arima_order, self.seasonal_order

    def train_models(self):
        """Train ARIMA and Holt-Winter models with manually selected parameters"""
        print("\n🤖 Training models with manually selected parameters...")

        # Train SARIMAX/ARIMA model with manual parameters
        print(f"Training SARIMAX with order={self.arima_order}, seasonal_order={self.seasonal_order}...")
        try:
            self.arima_model = SARIMAX(
                self.train_data['Demand'],
                exog=self.train_exog,
                order=self.arima_order,
                seasonal_order=self.seasonal_order,
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            self.arima_fitted = self.arima_model.fit(disp=False, maxiter=100)
            print(f"✓ SARIMAX trained with manual parameters - AIC: {self.arima_fitted.aic:.2f}")

        except Exception as e:
            print(f"SARIMAX failed: {e}, using simple ARIMA with manual order")
            self.arima_model = ARIMA(self.train_data['Demand'], order=self.arima_order)
            self.arima_fitted = self.arima_model.fit()
            print("✓ ARIMA fallback trained with manual parameters")
        
        # Train Holt-Winter model
        print("Training Holt-Winter...")
        try:
            self.hw_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24,
                initialization_method='estimated'
            )
            self.hw_fitted = self.hw_model.fit(optimized=True, remove_bias=True)
            print(f"✓ Holt-Winter trained - AIC: {self.hw_fitted.aic:.2f}")
            
        except Exception as e:
            print(f"Optimized Holt-Winter failed: {e}, using simple version")
            self.hw_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24
            )
            self.hw_fitted = self.hw_model.fit()
            print("✓ Simple Holt-Winter trained")
    
    def generate_step_predictions(self):
        """Generate predictions for specific steps through rolling forecasts"""
        print("\n📈 Generating step-by-step predictions...")
        
        # Define the steps we want to analyze
        target_steps = [1, 6, 12, 18, 24]
        
        # We'll generate predictions for a reasonable number of time points
        # Let's use the first 100 points of test data to have enough for visualization
        num_forecasts = min(100, len(self.test_data) - 24)  # Ensure we have enough data
        
        print(f"Generating {num_forecasts} forecasts for steps: {target_steps}")
        
        for i in range(num_forecasts):
            if i % 20 == 0:
                print(f"  Progress: {i}/{num_forecasts}")
            
            # For each forecasting origin, generate predictions for all target steps
            for step in target_steps:
                if i + step - 1 >= len(self.test_data):
                    continue
                
                # ARIMA prediction for this step
                try:
                    if hasattr(self.arima_fitted, 'get_forecast'):
                        # Use exogenous variables if available
                        if step <= len(self.test_exog) - i:
                            exog_step = self.test_exog.iloc[i:i + step]
                            forecast_result = self.arima_fitted.get_forecast(steps=step, exog=exog_step)
                            arima_pred = forecast_result.predicted_mean.iloc[-1]
                        else:
                            arima_pred = self.arima_fitted.forecast(steps=step)[-1]
                    else:
                        arima_pred = self.arima_fitted.forecast(steps=step)[-1]
                        
                except Exception as e:
                    arima_pred = self.train_data['Demand'].mean()
                
                # Holt-Winter prediction for this step
                try:
                    hw_pred = self.hw_fitted.forecast(steps=step)[-1]
                except Exception as e:
                    hw_pred = self.train_data['Demand'].mean()
                
                # Store predictions based on step
                if step == 1:
                    self.predictions_t1['arima'].append(arima_pred)
                    self.predictions_t1['holt_winter'].append(hw_pred)
                elif step == 6:
                    self.predictions_t6['arima'].append(arima_pred)
                    self.predictions_t6['holt_winter'].append(hw_pred)
                elif step == 12:
                    self.predictions_t12['arima'].append(arima_pred)
                    self.predictions_t12['holt_winter'].append(hw_pred)
                elif step == 18:
                    self.predictions_t18['arima'].append(arima_pred)
                    self.predictions_t18['holt_winter'].append(hw_pred)
                elif step == 24:
                    self.predictions_t24['arima'].append(arima_pred)
                    self.predictions_t24['holt_winter'].append(hw_pred)
        
        print("✓ Step predictions generated")
    
    def create_individual_charts(self):
        """Create individual charts for each forecasting step"""
        print("\n🎨 Creating individual charts...")
        
        # Get the corresponding actual values and time indices
        num_points = len(self.predictions_t1['arima'])
        actual_values = self.test_data['Demand'].iloc[:num_points]
        time_index = self.test_data.index[:num_points]
        
        # Chart 1: t+1
        plt.figure(figsize=(15, 7))
        plt.plot(time_index, actual_values, 'k-', label='Actual Demand', linewidth=2.5)
        plt.plot(time_index, self.predictions_t1['arima'], 'b-', label='ARIMA t+1', linewidth=2, alpha=0.8)
        plt.plot(time_index, self.predictions_t1['holt_winter'], 'r-', label='Holt-Winter t+1', linewidth=2, alpha=0.8)
        plt.title('Forecast performance at t+1', fontsize=16, fontweight='bold')
        plt.xlabel('Time', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('forecast_performance_t1.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Chart 2: t+6
        plt.figure(figsize=(15, 7))
        plt.plot(time_index, actual_values, 'k-', label='Actual Demand', linewidth=2.5)
        plt.plot(time_index, self.predictions_t6['arima'], 'b-', label='ARIMA t+6', linewidth=2, alpha=0.8)
        plt.plot(time_index, self.predictions_t6['holt_winter'], 'r-', label='Holt-Winter t+6', linewidth=2, alpha=0.8)
        plt.title('Forecast performance at t+6', fontsize=16, fontweight='bold')
        plt.xlabel('Time', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('forecast_performance_t6.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Chart 3: t+12
        plt.figure(figsize=(15, 7))
        plt.plot(time_index, actual_values, 'k-', label='Actual Demand', linewidth=2.5)
        plt.plot(time_index, self.predictions_t12['arima'], 'b-', label='ARIMA t+12', linewidth=2, alpha=0.8)
        plt.plot(time_index, self.predictions_t12['holt_winter'], 'r-', label='Holt-Winter t+12', linewidth=2, alpha=0.8)
        plt.title('Forecast performance at t+12', fontsize=16, fontweight='bold')
        plt.xlabel('Time', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('forecast_performance_t12.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Chart 4: t+18
        plt.figure(figsize=(15, 7))
        plt.plot(time_index, actual_values, 'k-', label='Actual Demand', linewidth=2.5)
        plt.plot(time_index, self.predictions_t18['arima'], 'b-', label='ARIMA t+18', linewidth=2, alpha=0.8)
        plt.plot(time_index, self.predictions_t18['holt_winter'], 'r-', label='Holt-Winter t+18', linewidth=2, alpha=0.8)
        plt.title('Forecast performance at t+18', fontsize=16, fontweight='bold')
        plt.xlabel('Time', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('forecast_performance_t18.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Chart 5: t+24
        plt.figure(figsize=(15, 7))
        plt.plot(time_index, actual_values, 'k-', label='Actual Demand', linewidth=2.5)
        plt.plot(time_index, self.predictions_t24['arima'], 'b-', label='ARIMA t+24', linewidth=2, alpha=0.8)
        plt.plot(time_index, self.predictions_t24['holt_winter'], 'r-', label='Holt-Winter t+24', linewidth=2, alpha=0.8)
        plt.title('Forecast performance at t+24', fontsize=16, fontweight='bold')
        plt.xlabel('Time', fontsize=12, fontweight='bold')
        plt.ylabel('Electric Demand (kW)', fontsize=12, fontweight='bold')
        plt.legend(fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('forecast_performance_t24.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("✓ All individual charts created and saved")
    
    def calculate_performance_metrics(self):
        """Calculate performance metrics for each step"""
        print("\n📊 Performance Metrics:")
        
        num_points = len(self.predictions_t1['arima'])
        actual_values = self.test_data['Demand'].iloc[:num_points].values
        
        steps = [1, 6, 12, 18, 24]
        predictions = [self.predictions_t1, self.predictions_t6, self.predictions_t12, 
                      self.predictions_t18, self.predictions_t24]
        
        print(f"{'Step':<6} {'ARIMA MAE':<12} {'H-W MAE':<12} {'Winner':<15}")
        print("-" * 50)
        
        for step, pred_dict in zip(steps, predictions):
            arima_mae = mean_absolute_error(actual_values, pred_dict['arima'])
            hw_mae = mean_absolute_error(actual_values, pred_dict['holt_winter'])
            
            winner = "Holt-Winter" if hw_mae < arima_mae else "ARIMA"
            
            print(f"t+{step:<4} {arima_mae:<12.2f} {hw_mae:<12.2f} {winner:<15}")
    
    def run_complete_analysis(self):
        """Run the complete individual step analysis"""
        print("🔍 STARTING INDIVIDUAL STEP FORECASTING ANALYSIS")
        print("Creating separate charts for t+1, t+6, t+12, t+18, t+24")
        print("="*80)
        
        try:
            # Step 1: Load and prepare data
            self.load_and_prepare_data()
            
            # Step 2: Train models
            self.train_models()
            
            # Step 3: Generate step predictions
            self.generate_step_predictions()
            
            # Step 4: Create individual charts
            self.create_individual_charts()
            
            # Step 5: Calculate performance metrics
            self.calculate_performance_metrics()
            
            print(f"\n✅ INDIVIDUAL STEP ANALYSIS COMPLETED!")
            print(f"📁 Generated 5 separate chart files:")
            print(f"   • forecast_performance_t1.png")
            print(f"   • forecast_performance_t6.png")
            print(f"   • forecast_performance_t12.png")
            print(f"   • forecast_performance_t18.png")
            print(f"   • forecast_performance_t24.png")
            
            return True
            
        except Exception as e:
            print(f"❌ Error during analysis: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main execution function"""
    print("🔍 INDIVIDUAL STEP FORECASTING ANALYSIS")
    print("Separate Charts for Each Forecasting Step")
    print("="*80)
    
    # Initialize analyzer
    analyzer = IndividualStepForecasting('electric_demand_1h.csv')
    
    # Run complete analysis
    success = analyzer.run_complete_analysis()
    
    if success:
        print(f"\n🎉 SUCCESS! Individual step analysis completed.")
        print(f"📊 5 separate charts generated showing forecast performance")
        print(f"📁 Each chart saved as separate PNG file")
        
    else:
        print("❌ Individual step analysis failed. Please check the error messages above.")


if __name__ == "__main__":
    main()
