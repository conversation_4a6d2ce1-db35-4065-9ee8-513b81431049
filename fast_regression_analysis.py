"""
FAST Electric Demand Regression Analysis
Quick regression modeling with essential features and faster hyperparameter tuning
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, TimeSeriesSplit
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import lightgbm as lgb

plt.style.use('seaborn-v0_8')

class FastRegressionAnalysis:
    def __init__(self, data_path):
        self.data_path = data_path
        self.data = None
        self.results = {}
        self.scaler = StandardScaler()
        
    def prepare_data(self):
        """Fast data preparation with essential features"""
        print("⚡ FAST REGRESSION ANALYSIS")
        print("="*50)
        print("📊 Loading data...")
        
        # Load data
        self.data = pd.read_csv(self.data_path)
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        
        print(f"Data: {len(self.data)} samples")
        
        # Essential feature engineering
        print("🔧 Creating features...")
        self.data['hour'] = self.data.index.hour
        self.data['day_of_week'] = self.data.index.dayofweek
        self.data['month'] = self.data.index.month
        
        # Cyclical features
        self.data['hour_sin'] = np.sin(2 * np.pi * self.data['hour'] / 24)
        self.data['hour_cos'] = np.cos(2 * np.pi * self.data['hour'] / 24)
        self.data['day_sin'] = np.sin(2 * np.pi * self.data['day_of_week'] / 7)
        self.data['day_cos'] = np.cos(2 * np.pi * self.data['day_of_week'] / 7)
        
        # Essential lag features
        self.data['demand_lag_1'] = self.data['Demand'].shift(1)
        self.data['demand_lag_24'] = self.data['Demand'].shift(24)
        
        # Rolling features
        self.data['demand_rolling_mean_24'] = self.data['Demand'].rolling(24).mean()
        self.data['temp_rolling_mean_24'] = self.data['Temperature'].rolling(24).mean()
        
        # Select features
        feature_cols = [
            'Temperature', 'temp_rolling_mean_24', 'Holiday', 'WeekDay',
            'hour', 'day_of_week', 'month',
            'hour_sin', 'hour_cos', 'day_sin', 'day_cos',
            'demand_lag_1', 'demand_lag_24', 'demand_rolling_mean_24'
        ]
        
        # Remove NaN rows
        self.data = self.data.dropna()
        
        self.X = self.data[feature_cols].copy()
        self.y = self.data['Demand'].copy()
        
        print(f"Features: {len(feature_cols)}")
        print(f"Clean samples: {len(self.X)}")
        
        return self.X, self.y
    
    def split_and_scale(self):
        """Split data and scale features"""
        print("\n📊 Splitting data...")
        
        # Time-based split
        split_idx = int(len(self.X) * 0.8)
        self.X_train = self.X.iloc[:split_idx]
        self.X_test = self.X.iloc[split_idx:]
        self.y_train = self.y.iloc[:split_idx]
        self.y_test = self.y.iloc[split_idx:]
        
        print(f"Train: {len(self.X_train)}, Test: {len(self.X_test)}")
        
        # Scale features
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
    def run_analysis(self):
        """Run complete regression analysis"""
        print("\n🤖 Running regression models...")
        
        # Initialize models
        models = {
            'Linear Regression': LinearRegression(),
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42),
            'XGBoost': xgb.XGBRegressor(n_estimators=100, random_state=42, verbosity=0),
            'LightGBM': lgb.LGBMRegressor(n_estimators=100, random_state=42, verbosity=-1)
        }
        
        # Cross-validation
        print("\n🔄 Cross-validation...")
        tscv = TimeSeriesSplit(n_splits=3)
        cv_results = {}
        
        for name, model in models.items():
            print(f"  {name}...")
            X_cv = self.X_train_scaled if name == 'Linear Regression' else self.X_train
            cv_scores = cross_val_score(model, X_cv, self.y_train, cv=tscv, 
                                      scoring='neg_mean_absolute_error', n_jobs=-1)
            cv_results[name] = {
                'cv_mae': -cv_scores.mean(),
                'cv_std': cv_scores.std()
            }
            print(f"    CV MAE: {cv_results[name]['cv_mae']:.2f} ± {cv_results[name]['cv_std']:.2f}")
        
        # Fast hyperparameter tuning
        print("\n🎯 Hyperparameter tuning...")
        best_models = {}
        
        # Linear Regression - no tuning needed
        best_models['Linear Regression'] = LinearRegression()
        
        # Random Forest - reduced grid
        rf_params = {'n_estimators': [100, 200], 'max_depth': [10, 20]}
        rf_grid = GridSearchCV(RandomForestRegressor(random_state=42), rf_params, 
                              cv=tscv, scoring='neg_mean_absolute_error', n_jobs=-1)
        rf_grid.fit(self.X_train, self.y_train)
        best_models['Random Forest'] = rf_grid.best_estimator_
        print(f"  Random Forest best: {rf_grid.best_params_}")
        
        # XGBoost - reduced grid
        xgb_params = {'n_estimators': [100, 200], 'max_depth': [3, 6], 'learning_rate': [0.1, 0.2]}
        xgb_grid = GridSearchCV(xgb.XGBRegressor(random_state=42, verbosity=0), xgb_params,
                               cv=tscv, scoring='neg_mean_absolute_error', n_jobs=-1)
        xgb_grid.fit(self.X_train, self.y_train)
        best_models['XGBoost'] = xgb_grid.best_estimator_
        print(f"  XGBoost best: {xgb_grid.best_params_}")
        
        # LightGBM - reduced grid
        lgb_params = {'n_estimators': [100, 200], 'max_depth': [3, 6], 'learning_rate': [0.1, 0.2]}
        lgb_grid = GridSearchCV(lgb.LGBMRegressor(random_state=42, verbosity=-1), lgb_params,
                               cv=tscv, scoring='neg_mean_absolute_error', n_jobs=-1)
        lgb_grid.fit(self.X_train, self.y_train)
        best_models['LightGBM'] = lgb_grid.best_estimator_
        print(f"  LightGBM best: {lgb_grid.best_params_}")
        
        # Train and evaluate
        print("\n📊 Final evaluation...")
        test_results = {}
        
        for name, model in best_models.items():
            # Train
            X_train_use = self.X_train_scaled if name == 'Linear Regression' else self.X_train
            X_test_use = self.X_test_scaled if name == 'Linear Regression' else self.X_test
            
            model.fit(X_train_use, self.y_train)
            
            # Predict
            y_pred = model.predict(X_test_use)
            
            # Metrics
            mae = mean_absolute_error(self.y_test, y_pred)
            rmse = np.sqrt(mean_squared_error(self.y_test, y_pred))
            r2 = r2_score(self.y_test, y_pred)
            mape = np.mean(np.abs((self.y_test - y_pred) / self.y_test)) * 100
            
            test_results[name] = {
                'mae': mae, 'rmse': rmse, 'r2': r2, 'mape': mape, 'predictions': y_pred
            }
            
            print(f"  {name}: MAE={mae:.2f}, R²={r2:.4f}, MAPE={mape:.2f}%")
        
        # Find best model
        best_model_name = min(test_results.keys(), key=lambda x: test_results[x]['mae'])
        best_model = best_models[best_model_name]
        
        print(f"\n🏆 Best Model: {best_model_name}")
        print(f"📊 Best MAE: {test_results[best_model_name]['mae']:.2f}")
        print(f"📊 Best R²: {test_results[best_model_name]['r2']:.4f}")
        
        # Store results
        self.results = {
            'cv_results': cv_results,
            'test_results': test_results,
            'best_model_name': best_model_name,
            'best_model': best_model
        }
        
        return test_results, best_model_name
    
    def visualize_best_model(self):
        """Visualize the best model results"""
        print("\n🎨 Creating visualizations...")
        
        best_name = self.results['best_model_name']
        best_results = self.results['test_results'][best_name]
        y_pred = best_results['predictions']
        
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 1. Model comparison
        models = list(self.results['test_results'].keys())
        maes = [self.results['test_results'][m]['mae'] for m in models]
        r2s = [self.results['test_results'][m]['r2'] for m in models]
        
        bars = axes[0,0].bar(models, maes, alpha=0.8)
        # Highlight best model
        best_idx = models.index(best_name)
        bars[best_idx].set_color('gold')
        bars[best_idx].set_edgecolor('red')
        bars[best_idx].set_linewidth(3)
        
        axes[0,0].set_title('Model Comparison - MAE', fontsize=14, fontweight='bold')
        axes[0,0].set_ylabel('Mean Absolute Error')
        axes[0,0].tick_params(axis='x', rotation=45)
        axes[0,0].grid(True, alpha=0.3)
        
        # Add value labels
        for bar, mae in zip(bars, maes):
            height = bar.get_height()
            axes[0,0].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{mae:.0f}', ha='center', va='bottom', fontweight='bold')
        
        # 2. R² comparison
        bars2 = axes[0,1].bar(models, r2s, alpha=0.8, color='green')
        bars2[best_idx].set_color('gold')
        bars2[best_idx].set_edgecolor('red')
        bars2[best_idx].set_linewidth(3)
        
        axes[0,1].set_title('Model Comparison - R² Score', fontsize=14, fontweight='bold')
        axes[0,1].set_ylabel('R² Score')
        axes[0,1].tick_params(axis='x', rotation=45)
        axes[0,1].grid(True, alpha=0.3)
        
        for bar, r2 in zip(bars2, r2s):
            height = bar.get_height()
            axes[0,1].text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                          f'{r2:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 3. Best model: Time series plot
        axes[1,0].plot(self.y_test.index, self.y_test.values, 'b-', label='Actual', linewidth=2)
        axes[1,0].plot(self.y_test.index, y_pred, 'r-', label='Predicted', linewidth=2, alpha=0.8)
        axes[1,0].set_title(f'Best Model ({best_name}): Actual vs Predicted', 
                           fontsize=14, fontweight='bold')
        axes[1,0].set_ylabel('Electric Demand (kW)')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)
        axes[1,0].tick_params(axis='x', rotation=45)
        
        # 4. Best model: Scatter plot
        axes[1,1].scatter(self.y_test, y_pred, alpha=0.6, s=10)
        
        # Perfect prediction line
        min_val = min(self.y_test.min(), y_pred.min())
        max_val = max(self.y_test.max(), y_pred.max())
        axes[1,1].plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2)
        
        axes[1,1].set_xlabel('Actual Demand (kW)')
        axes[1,1].set_ylabel('Predicted Demand (kW)')
        axes[1,1].set_title(f'Best Model ({best_name}): Scatter Plot', fontsize=14, fontweight='bold')
        axes[1,1].grid(True, alpha=0.3)
        
        # Add metrics text
        metrics_text = f'R² = {best_results["r2"]:.4f}\nMAE = {best_results["mae"]:.2f}\nMAPE = {best_results["mape"]:.2f}%'
        axes[1,1].text(0.05, 0.95, metrics_text, transform=axes[1,1].transAxes,
                      verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8),
                      fontsize=12, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig('fast_regression_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Print summary
        print("\n" + "="*60)
        print("📊 REGRESSION ANALYSIS RESULTS")
        print("="*60)
        print(f"🏆 Best Model: {best_name}")
        print(f"📊 MAE:  {best_results['mae']:.2f} kW")
        print(f"📊 RMSE: {best_results['rmse']:.2f} kW")
        print(f"📊 R²:   {best_results['r2']:.4f}")
        print(f"📊 MAPE: {best_results['mape']:.2f}%")
        print("="*60)


def main():
    """Main execution"""
    analyzer = FastRegressionAnalysis('electric_demand_1h.csv')
    
    # Run analysis
    analyzer.prepare_data()
    analyzer.split_and_scale()
    analyzer.run_analysis()
    analyzer.visualize_best_model()
    
    print(f"\n✅ Analysis completed!")
    print(f"📁 Results saved as 'fast_regression_results.png'")


if __name__ == "__main__":
    main()
