import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.stattools import acf, pacf
import warnings
warnings.filterwarnings('ignore')

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

def load_and_prepare_data(file_path):
    """Load and prepare the electric demand data"""
    print("=== Phase 1: Data Preparation ===")
    
    # Load data
    df = pd.read_csv(file_path)
    print(f"Data loaded: {df.shape[0]} records, {df.shape[1]} columns")
    
    # Create datetime index
    df['DateTime'] = pd.to_datetime(df[['Year', 'Month', 'Day', 'Hour']])
    df.set_index('DateTime', inplace=True)
    df.index.freq = 'H'  # Set hourly frequency
    
    # Keep only necessary columns
    df = df[['WeekDay', 'Holiday', 'Temperature', 'Demand']]
    
    print(f"DateTime index created with frequency: {df.index.freq}")
    print(f"Date range: {df.index.min()} to {df.index.max()}")
    
    # Verify no missing values
    print(f"Missing values: {df.isnull().sum().sum()}")
    
    return df

def encode_exogenous_variables(df):
    """Encode categorical exogenous variables"""
    print("\n=== Encoding Exogenous Variables ===")
    
    # Create dummy variables for WeekDay (drop one to avoid multicollinearity)
    weekday_dummies = pd.get_dummies(df['WeekDay'], prefix='WeekDay', drop_first=True)
    
    # Holiday is already binary (0/1)
    holiday_dummy = df['Holiday']
    
    # Temperature is already numerical
    temperature = df['Temperature']
    
    # Combine exogenous variables
    exog_vars = pd.concat([weekday_dummies, holiday_dummy, temperature], axis=1)
    exog_vars.columns = list(weekday_dummies.columns) + ['Holiday'] + ['Temperature']
    
    print(f"Exogenous variables created: {list(exog_vars.columns)}")
    print(f"Shape: {exog_vars.shape}")
    
    return exog_vars

def split_data(df, exog_vars, train_ratio=0.65, test_ratio=0.175):
    """Split data chronologically: 65% train, 17.5% test, 17.5% validation"""
    print(f"\n=== Data Splitting ===")
    
    n = len(df)
    train_size = int(n * train_ratio)
    test_size = int(n * test_ratio)
    
    # Split indices
    train_end = train_size
    test_end = train_size + test_size
    
    # Split data
    train_data = df.iloc[:train_end].copy()
    test_data = df.iloc[train_end:test_end].copy()
    val_data = df.iloc[test_end:].copy()
    
    # Split exogenous variables
    train_exog = exog_vars.iloc[:train_end].copy()
    test_exog = exog_vars.iloc[train_end:test_end].copy()
    val_exog = exog_vars.iloc[test_end:].copy()
    
    print(f"Training set: {len(train_data)} records ({len(train_data)/n*100:.1f}%)")
    print(f"Test set: {len(test_data)} records ({len(test_data)/n*100:.1f}%)")
    print(f"Validation set: {len(val_data)} records ({len(val_data)/n*100:.1f}%)")
    print(f"Training period: {train_data.index.min()} to {train_data.index.max()}")
    print(f"Test period: {test_data.index.min()} to {test_data.index.max()}")
    print(f"Validation period: {val_data.index.min()} to {val_data.index.max()}")
    
    return (train_data, test_data, val_data), (train_exog, test_exog, val_exog)

def perform_eda(train_data):
    """Perform Exploratory Data Analysis"""
    print(f"\n=== Exploratory Data Analysis ===")
    
    # Basic statistics
    print("Demand statistics:")
    print(train_data['Demand'].describe())
    
    # Create subplots for EDA
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Exploratory Data Analysis - Training Data', fontsize=16)
    
    # 1. Time series plot
    axes[0,0].plot(train_data.index, train_data['Demand'], alpha=0.7, linewidth=0.5)
    axes[0,0].set_title('Electric Demand Over Time')
    axes[0,0].set_ylabel('Demand')
    axes[0,0].tick_params(axis='x', rotation=45)
    
    # 2. Demand vs Temperature
    axes[0,1].scatter(train_data['Temperature'], train_data['Demand'], alpha=0.5, s=1)
    axes[0,1].set_title('Demand vs Temperature')
    axes[0,1].set_xlabel('Temperature')
    axes[0,1].set_ylabel('Demand')
    
    # 3. Average demand by hour
    hourly_avg = train_data.groupby(train_data.index.hour)['Demand'].mean()
    axes[1,0].plot(hourly_avg.index, hourly_avg.values, marker='o')
    axes[1,0].set_title('Average Demand by Hour of Day')
    axes[1,0].set_xlabel('Hour')
    axes[1,0].set_ylabel('Average Demand')
    axes[1,0].set_xticks(range(0, 24, 2))
    
    # 4. Average demand by weekday
    weekday_avg = train_data.groupby('WeekDay')['Demand'].mean()
    axes[1,1].bar(weekday_avg.index, weekday_avg.values)
    axes[1,1].set_title('Average Demand by WeekDay')
    axes[1,1].set_xlabel('WeekDay (1=Mon, 7=Sun)')
    axes[1,1].set_ylabel('Average Demand')
    
    plt.tight_layout()
    plt.show()
    
    # Holiday effect
    fig, ax = plt.subplots(1, 1, figsize=(10, 6))
    holiday_comparison = train_data.groupby('Holiday')['Demand'].mean()
    ax.bar(['Non-Holiday', 'Holiday'], holiday_comparison.values)
    ax.set_title('Average Demand: Holiday vs Non-Holiday')
    ax.set_ylabel('Average Demand')
    plt.show()

def time_series_decomposition(train_data):
    """Perform time series decomposition"""
    print(f"\n=== Time Series Decomposition ===")
    
    # Decomposition with daily seasonality (24 hours)
    decomposition_daily = seasonal_decompose(train_data['Demand'], 
                                           model='additive', 
                                           period=24)
    
    # Plot decomposition
    fig, axes = plt.subplots(4, 1, figsize=(15, 12))
    fig.suptitle('Time Series Decomposition (Daily Seasonality - 24h)', fontsize=16)
    
    decomposition_daily.observed.plot(ax=axes[0], title='Original')
    decomposition_daily.trend.plot(ax=axes[1], title='Trend')
    decomposition_daily.seasonal.plot(ax=axes[2], title='Seasonal (24h)')
    decomposition_daily.resid.plot(ax=axes[3], title='Residual')
    
    plt.tight_layout()
    plt.show()
    
    return decomposition_daily

def adf_test(series, title=""):
    """Perform Augmented Dickey-Fuller test for stationarity"""
    result = adfuller(series.dropna())

    print(f'\n=== ADF Test Results {title} ===')
    print(f'ADF Statistic: {result[0]:.6f}')
    print(f'p-value: {result[1]:.6f}')
    print(f'Critical Values:')
    for key, value in result[4].items():
        print(f'\t{key}: {value:.3f}')

    if result[1] <= 0.05:
        print("Result: Series is STATIONARY (reject null hypothesis)")
        return True
    else:
        print("Result: Series is NON-STATIONARY (fail to reject null hypothesis)")
        return False

def test_stationarity_and_difference(train_data):
    """Test stationarity and apply differencing if needed"""
    print(f"\n=== Phase 2: Stationarity Testing and Differencing ===")

    demand_series = train_data['Demand']

    # Test original series
    is_stationary = adf_test(demand_series, "- Original Series")

    # Store different versions for comparison
    series_dict = {'Original': demand_series}

    # Apply first differencing if not stationary
    if not is_stationary:
        diff_1 = demand_series.diff().dropna()
        series_dict['First Difference'] = diff_1
        is_stationary_diff1 = adf_test(diff_1, "- First Differenced")

        # Apply seasonal differencing (24 hours)
        seasonal_diff_24 = demand_series.diff(24).dropna()
        series_dict['Seasonal Diff (24h)'] = seasonal_diff_24
        adf_test(seasonal_diff_24, "- Seasonal Differenced (24h)")

        # Apply both first and seasonal differencing
        combined_diff = demand_series.diff().diff(24).dropna()
        series_dict['Combined Diff (1st + 24h)'] = combined_diff
        adf_test(combined_diff, "- Combined Differencing")

        # Weekly seasonal differencing (168 hours)
        if len(demand_series) > 168:
            seasonal_diff_168 = demand_series.diff(168).dropna()
            series_dict['Seasonal Diff (168h)'] = seasonal_diff_168
            adf_test(seasonal_diff_168, "- Seasonal Differenced (168h)")

    # Plot different series
    n_series = len(series_dict)
    fig, axes = plt.subplots(n_series, 1, figsize=(15, 4*n_series))
    if n_series == 1:
        axes = [axes]

    fig.suptitle('Stationarity Testing: Original vs Differenced Series', fontsize=16)

    for i, (name, series) in enumerate(series_dict.items()):
        axes[i].plot(series.index, series.values, linewidth=0.7)
        axes[i].set_title(f'{name} Series')
        axes[i].set_ylabel('Value')
        axes[i].tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.show()

    return series_dict

def plot_acf_pacf_analysis(series_dict):
    """Plot ACF and PACF for different series"""
    print(f"\n=== ACF and PACF Analysis ===")

    for name, series in series_dict.items():
        if len(series.dropna()) < 50:  # Skip if too few observations
            continue

        print(f"\nAnalyzing: {name}")

        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'ACF/PACF Analysis - {name}', fontsize=16)

        # Time series plot
        axes[0,0].plot(series.index, series.values, linewidth=0.7)
        axes[0,0].set_title(f'{name} Time Series')
        axes[0,0].set_ylabel('Value')

        # Histogram
        axes[0,1].hist(series.dropna(), bins=50, alpha=0.7, edgecolor='black')
        axes[0,1].set_title(f'{name} Distribution')
        axes[0,1].set_xlabel('Value')
        axes[0,1].set_ylabel('Frequency')

        # ACF plot
        plot_acf(series.dropna(), ax=axes[1,0], lags=min(100, len(series)//4), alpha=0.05)
        axes[1,0].set_title(f'Autocorrelation Function (ACF)')

        # PACF plot
        plot_pacf(series.dropna(), ax=axes[1,1], lags=min(50, len(series)//8), alpha=0.05)
        axes[1,1].set_title(f'Partial Autocorrelation Function (PACF)')

        plt.tight_layout()
        plt.show()

def count_significant_lags(correlation_values, alpha=0.05):
    """Count significant lags in ACF/PACF before cutoff"""
    n = len(correlation_values)
    # Calculate confidence interval
    confidence_interval = 1.96 / np.sqrt(n)  # 95% confidence interval

    significant_count = 0
    for i in range(1, min(len(correlation_values), 20)):  # Skip lag 0, check up to 20 lags
        if abs(correlation_values[i]) > confidence_interval:
            significant_count = i
        else:
            break  # Stop at first non-significant lag (cutoff pattern)

    return significant_count

def suggest_arima_parameters(series_dict):
    """Analyze ACF/PACF and suggest ARIMA parameters"""
    print(f"\n=== AUTOMATED ARIMA PARAMETER SUGGESTIONS ===")

    suggestions = {}

    for name, series in series_dict.items():
        if len(series.dropna()) < 100:  # Need sufficient data
            continue

        print(f"\n--- Analyzing: {name} ---")

        # Calculate ACF and PACF values
        series_clean = series.dropna()
        max_lags = min(50, len(series_clean)//4)

        try:
            acf_values = acf(series_clean, nlags=max_lags, alpha=0.05)
            pacf_values = pacf(series_clean, nlags=max_lags, alpha=0.05)

            # Count significant lags
            q_suggest = count_significant_lags(acf_values[0])  # ACF for MA order
            p_suggest = count_significant_lags(pacf_values[0])  # PACF for AR order

            # Determine d based on series type
            if 'Original' in name:
                d_suggest = 0
            elif 'First Difference' in name or 'Combined' in name:
                d_suggest = 1
            else:
                d_suggest = 1

            # Check for seasonal patterns
            seasonal_suggestions = []

            # Daily seasonality (24h)
            if len(acf_values[0]) > 24:
                daily_acf_24 = abs(acf_values[0][24]) if len(acf_values[0]) > 24 else 0
                daily_pacf_24 = abs(pacf_values[0][24]) if len(pacf_values[0]) > 24 else 0

                if daily_acf_24 > 0.1 or daily_pacf_24 > 0.1:  # Significant seasonal pattern
                    seasonal_suggestions.append({
                        'period': 24,
                        'P': 1 if daily_pacf_24 > 0.15 else 0,
                        'D': 1,
                        'Q': 1 if daily_acf_24 > 0.15 else 0
                    })

            # Weekly seasonality (168h)
            if len(acf_values[0]) > 168:
                weekly_acf_168 = abs(acf_values[0][168]) if len(acf_values[0]) > 168 else 0
                weekly_pacf_168 = abs(pacf_values[0][168]) if len(pacf_values[0]) > 168 else 0

                if weekly_acf_168 > 0.1 or weekly_pacf_168 > 0.1:
                    seasonal_suggestions.append({
                        'period': 168,
                        'P': 1 if weekly_pacf_168 > 0.15 else 0,
                        'D': 1,
                        'Q': 1 if weekly_acf_168 > 0.15 else 0
                    })

            # Store suggestions
            suggestions[name] = {
                'non_seasonal': {
                    'p': min(p_suggest, 3),  # Cap at 3 for computational efficiency
                    'd': d_suggest,
                    'q': min(q_suggest, 3)
                },
                'seasonal': seasonal_suggestions,
                'acf_significant_lags': q_suggest,
                'pacf_significant_lags': p_suggest
            }

            # Print analysis
            print(f"ACF Analysis: {q_suggest} significant lags → q = {min(q_suggest, 3)}")
            print(f"PACF Analysis: {p_suggest} significant lags → p = {min(p_suggest, 3)}")
            print(f"Differencing: d = {d_suggest}")

            if seasonal_suggestions:
                print("Seasonal patterns detected:")
                for seasonal in seasonal_suggestions:
                    print(f"  - Period {seasonal['period']}: (P,D,Q) = ({seasonal['P']},{seasonal['D']},{seasonal['Q']})")
            else:
                print("No strong seasonal patterns detected")

        except Exception as e:
            print(f"Error analyzing {name}: {str(e)}")
            continue

    return suggestions

def recommend_best_models(suggestions):
    """Recommend the best SARIMA models to try"""
    print(f"\n=== RECOMMENDED SARIMA MODELS TO TEST ===")

    # Find the best stationary series
    stationary_series = []
    for name in suggestions.keys():
        if 'Difference' in name or 'Combined' in name:
            stationary_series.append(name)

    if not stationary_series:
        stationary_series = list(suggestions.keys())

    print("Based on the analysis, here are the recommended models to test:")
    print("(Listed in order of priority)\n")

    model_recommendations = []

    for i, series_name in enumerate(stationary_series[:2]):  # Top 2 series
        if series_name not in suggestions:
            continue

        suggestion = suggestions[series_name]
        p, d, q = suggestion['non_seasonal']['p'], suggestion['non_seasonal']['d'], suggestion['non_seasonal']['q']

        print(f"--- Based on {series_name} ---")

        # Model 1: Basic SARIMA with daily seasonality
        if suggestion['seasonal']:
            for seasonal in suggestion['seasonal']:
                if seasonal['period'] == 24:  # Daily seasonality
                    P, D, Q, s = seasonal['P'], seasonal['D'], seasonal['Q'], seasonal['period']
                    model_recommendations.append(f"SARIMA({p},{d},{q})({P},{D},{Q}){s}")
                    print(f"1. SARIMA({p},{d},{q})({P},{D},{Q}){s} - Daily seasonality")

        # Model 2: Simple ARIMA (no seasonality)
        model_recommendations.append(f"ARIMA({p},{d},{q})")
        print(f"2. ARIMA({p},{d},{q}) - No seasonality")

        # Model 3: Alternative orders (±1 from suggested)
        p_alt = max(1, p-1) if p > 1 else p+1
        q_alt = max(1, q-1) if q > 1 else q+1
        model_recommendations.append(f"ARIMA({p_alt},{d},{q_alt})")
        print(f"3. ARIMA({p_alt},{d},{q_alt}) - Alternative orders")

        print()

    # Add some standard models for electric demand
    print("--- Standard Electric Demand Models ---")
    standard_models = [
        "SARIMA(1,1,1)(1,1,1)24",
        "SARIMA(2,1,2)(1,1,1)24",
        "SARIMA(1,1,1)(2,1,2)24",
        "SARIMA(0,1,1)(0,1,1)24"
    ]

    for model in standard_models:
        if model not in model_recommendations:
            model_recommendations.append(model)
            print(f"• {model}")

    print(f"\n=== NEXT STEPS ===")
    print("1. Test these models using auto_arima for validation")
    print("2. Compare AIC/BIC values to select the best model")
    print("3. Perform residual diagnostics on the chosen model")
    print("4. Use the best model for forecasting")

    return model_recommendations

def visualize_parameter_analysis(series_dict, parameter_suggestions):
    """Create comprehensive visualizations of the parameter analysis"""
    print(f"\n=== PARAMETER ANALYSIS VISUALIZATION ===")

    # Create summary visualization
    fig = plt.figure(figsize=(20, 15))
    gs = fig.add_gridspec(4, 4, hspace=0.3, wspace=0.3)

    # Title
    fig.suptitle('ARIMA Parameter Analysis Summary', fontsize=20, fontweight='bold')

    # 1. Stationarity Test Results Summary
    ax1 = fig.add_subplot(gs[0, :2])
    series_names = list(series_dict.keys())
    stationarity_results = []

    for name in series_names:
        series = series_dict[name]
        try:
            adf_result = adfuller(series.dropna())
            is_stationary = adf_result[1] <= 0.05
            stationarity_results.append(1 if is_stationary else 0)
        except:
            stationarity_results.append(0)

    colors = ['green' if x == 1 else 'red' for x in stationarity_results]
    bars = ax1.bar(range(len(series_names)), stationarity_results, color=colors, alpha=0.7)
    ax1.set_title('Stationarity Test Results (ADF Test)', fontweight='bold')
    ax1.set_ylabel('Stationary (1) / Non-Stationary (0)')
    ax1.set_xticks(range(len(series_names)))
    ax1.set_xticklabels(series_names, rotation=45, ha='right')
    ax1.set_ylim(0, 1.2)

    # Add value labels on bars
    for bar, result in zip(bars, stationarity_results):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                'Stationary' if result == 1 else 'Non-Stationary',
                ha='center', va='bottom', fontweight='bold')

    # 2. Suggested Parameters Summary
    ax2 = fig.add_subplot(gs[0, 2:])
    if parameter_suggestions:
        param_data = []
        param_labels = []

        for name, suggestion in parameter_suggestions.items():
            if 'non_seasonal' in suggestion:
                p = suggestion['non_seasonal']['p']
                d = suggestion['non_seasonal']['d']
                q = suggestion['non_seasonal']['q']
                param_data.append([p, d, q])
                param_labels.append(name)

        if param_data:
            param_array = np.array(param_data)
            x = np.arange(len(param_labels))
            width = 0.25

            ax2.bar(x - width, param_array[:, 0], width, label='p (AR)', alpha=0.8)
            ax2.bar(x, param_array[:, 1], width, label='d (Diff)', alpha=0.8)
            ax2.bar(x + width, param_array[:, 2], width, label='q (MA)', alpha=0.8)

            ax2.set_title('Suggested ARIMA Parameters (p,d,q)', fontweight='bold')
            ax2.set_ylabel('Parameter Value')
            ax2.set_xticks(x)
            ax2.set_xticklabels(param_labels, rotation=45, ha='right')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

    # 3. ACF/PACF Significant Lags Comparison
    ax3 = fig.add_subplot(gs[1, :2])
    if parameter_suggestions:
        acf_lags = []
        pacf_lags = []
        series_labels = []

        for name, suggestion in parameter_suggestions.items():
            if 'acf_significant_lags' in suggestion:
                acf_lags.append(suggestion['acf_significant_lags'])
                pacf_lags.append(suggestion['pacf_significant_lags'])
                series_labels.append(name)

        if acf_lags:
            x = np.arange(len(series_labels))
            width = 0.35

            ax3.bar(x - width/2, acf_lags, width, label='ACF Significant Lags (→q)', alpha=0.8, color='skyblue')
            ax3.bar(x + width/2, pacf_lags, width, label='PACF Significant Lags (→p)', alpha=0.8, color='lightcoral')

            ax3.set_title('ACF vs PACF Significant Lags Analysis', fontweight='bold')
            ax3.set_ylabel('Number of Significant Lags')
            ax3.set_xticks(x)
            ax3.set_xticklabels(series_labels, rotation=45, ha='right')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # Add value labels
            for i, (acf_val, pacf_val) in enumerate(zip(acf_lags, pacf_lags)):
                ax3.text(i - width/2, acf_val + 0.1, str(acf_val), ha='center', va='bottom')
                ax3.text(i + width/2, pacf_val + 0.1, str(pacf_val), ha='center', va='bottom')

    # 4. Seasonal Patterns Detection
    ax4 = fig.add_subplot(gs[1, 2:])
    seasonal_data = {'Daily (24h)': [], 'Weekly (168h)': []}
    seasonal_series = []

    for name, suggestion in parameter_suggestions.items():
        if 'seasonal' in suggestion and suggestion['seasonal']:
            seasonal_series.append(name)
            daily_found = any(s['period'] == 24 for s in suggestion['seasonal'])
            weekly_found = any(s['period'] == 168 for s in suggestion['seasonal'])
            seasonal_data['Daily (24h)'].append(1 if daily_found else 0)
            seasonal_data['Weekly (168h)'].append(1 if weekly_found else 0)

    if seasonal_series:
        x = np.arange(len(seasonal_series))
        width = 0.35

        ax4.bar(x - width/2, seasonal_data['Daily (24h)'], width,
                label='Daily Seasonality', alpha=0.8, color='gold')
        ax4.bar(x + width/2, seasonal_data['Weekly (168h)'], width,
                label='Weekly Seasonality', alpha=0.8, color='orange')

        ax4.set_title('Seasonal Patterns Detection', fontweight='bold')
        ax4.set_ylabel('Pattern Detected (1=Yes, 0=No)')
        ax4.set_xticks(x)
        ax4.set_xticklabels(seasonal_series, rotation=45, ha='right')
        ax4.legend()
        ax4.set_ylim(0, 1.2)
        ax4.grid(True, alpha=0.3)

    # 5-8. Individual ACF/PACF plots for top series
    plot_positions = [(2, 0), (2, 1), (3, 0), (3, 1)]
    stationary_series = [name for name in series_dict.keys() if 'Difference' in name or 'Combined' in name]

    for i, (row, col) in enumerate(plot_positions):
        if i < len(stationary_series):
            series_name = stationary_series[i]
            series = series_dict[series_name]

            ax = fig.add_subplot(gs[row, col])

            # Plot both ACF and PACF on same subplot
            series_clean = series.dropna()
            max_lags = min(20, len(series_clean)//10)

            try:
                acf_vals = acf(series_clean, nlags=max_lags, alpha=0.05)
                pacf_vals = pacf(series_clean, nlags=max_lags, alpha=0.05)

                lags = range(max_lags + 1)
                ax.stem(lags, acf_vals[0], linefmt='b-', markerfmt='bo', basefmt=' ', label='ACF')
                ax.stem([l + 0.1 for l in lags], pacf_vals[0], linefmt='r-', markerfmt='rs', basefmt=' ', label='PACF')

                # Add confidence intervals
                n = len(series_clean)
                confidence_interval = 1.96 / np.sqrt(n)
                ax.axhline(y=confidence_interval, color='gray', linestyle='--', alpha=0.5)
                ax.axhline(y=-confidence_interval, color='gray', linestyle='--', alpha=0.5)

                ax.set_title(f'{series_name}\nACF & PACF', fontsize=10, fontweight='bold')
                ax.set_xlabel('Lag')
                ax.set_ylabel('Correlation')
                ax.legend(fontsize=8)
                ax.grid(True, alpha=0.3)

            except Exception as e:
                ax.text(0.5, 0.5, f'Error plotting\n{series_name}',
                       ha='center', va='center', transform=ax.transAxes)

    plt.tight_layout()
    plt.show()

def create_model_comparison_table(recommended_models, parameter_suggestions):
    """Create a visual table of recommended models"""
    print(f"\n=== RECOMMENDED MODELS TABLE ===")

    fig, ax = plt.subplots(figsize=(12, 8))
    ax.axis('tight')
    ax.axis('off')

    # Prepare table data
    table_data = []
    headers = ['Rank', 'Model', 'Type', 'Seasonality', 'Complexity', 'Recommendation']

    for i, model in enumerate(recommended_models[:8]):  # Top 8 models
        rank = i + 1

        # Determine model type and characteristics
        if 'SARIMA' in model:
            model_type = 'SARIMA'
            seasonality = 'Yes'
            # Extract parameters to determine complexity
            if '(2,' in model or ',2,' in model or ',2)' in model:
                complexity = 'High'
            elif '(1,' in model or ',1,' in model or ',1)' in model:
                complexity = 'Medium'
            else:
                complexity = 'Low'
        else:
            model_type = 'ARIMA'
            seasonality = 'No'
            if '(2,' in model or ',2,' in model or ',2)' in model:
                complexity = 'Medium'
            else:
                complexity = 'Low'

        # Recommendation based on rank
        if rank <= 2:
            recommendation = '⭐⭐⭐ High Priority'
        elif rank <= 4:
            recommendation = '⭐⭐ Medium Priority'
        else:
            recommendation = '⭐ Test if needed'

        table_data.append([rank, model, model_type, seasonality, complexity, recommendation])

    # Create table
    table = ax.table(cellText=table_data, colLabels=headers, cellLoc='center', loc='center')
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 2)

    # Style the table
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')

    # Color code rows by priority
    for i in range(1, len(table_data) + 1):
        if i <= 2:
            color = '#E8F5E8'  # Light green for high priority
        elif i <= 4:
            color = '#FFF3E0'  # Light orange for medium priority
        else:
            color = '#F5F5F5'  # Light gray for low priority

        for j in range(len(headers)):
            table[(i, j)].set_facecolor(color)

    plt.title('Recommended SARIMA Models for Electric Demand Forecasting',
              fontsize=14, fontweight='bold', pad=20)
    plt.show()

def visualize_series_comparison(series_dict):
    """Create a comparison visualization of all differenced series"""
    print(f"\n=== SERIES COMPARISON VISUALIZATION ===")

    n_series = len(series_dict)
    fig, axes = plt.subplots(n_series, 1, figsize=(15, 4*n_series))
    if n_series == 1:
        axes = [axes]

    fig.suptitle('Comparison of Original vs Differenced Series', fontsize=16, fontweight='bold')

    for i, (name, series) in enumerate(series_dict.items()):
        # Plot the series
        axes[i].plot(series.index, series.values, linewidth=0.8, alpha=0.8)
        axes[i].set_title(f'{name} Series', fontweight='bold')
        axes[i].set_ylabel('Value')
        axes[i].grid(True, alpha=0.3)

        # Add statistics text box
        stats_text = f'Mean: {series.mean():.2f}\nStd: {series.std():.2f}\nMin: {series.min():.2f}\nMax: {series.max():.2f}'
        axes[i].text(0.02, 0.98, stats_text, transform=axes[i].transAxes,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        # Add stationarity indicator
        try:
            adf_result = adfuller(series.dropna())
            is_stationary = adf_result[1] <= 0.05
            status_text = f'ADF p-value: {adf_result[1]:.4f}\nStatus: {"STATIONARY" if is_stationary else "NON-STATIONARY"}'
            color = 'lightgreen' if is_stationary else 'lightcoral'
            axes[i].text(0.98, 0.98, status_text, transform=axes[i].transAxes,
                        verticalalignment='top', horizontalalignment='right',
                        bbox=dict(boxstyle='round', facecolor=color, alpha=0.8))
        except:
            pass

    plt.tight_layout()
    plt.show()

def main():
    """Main execution function"""
    # Load and prepare data
    df = load_and_prepare_data('electric_demand_1h.csv')

    # Encode exogenous variables
    exog_vars = encode_exogenous_variables(df)

    # Split data
    (train_data, test_data, val_data), (train_exog, test_exog, val_exog) = split_data(df, exog_vars)

    # Perform EDA
    perform_eda(train_data)

    # Time series decomposition
    decomposition = time_series_decomposition(train_data)

    # Test stationarity and differencing
    series_dict = test_stationarity_and_difference(train_data)

    # ACF/PACF analysis
    plot_acf_pacf_analysis(series_dict)

    # Automated parameter suggestions
    parameter_suggestions = suggest_arima_parameters(series_dict)

    # Recommend best models
    recommended_models = recommend_best_models(parameter_suggestions)

    # Create comprehensive visualizations
    print(f"\n=== CREATING COMPREHENSIVE VISUALIZATIONS ===")

    # 1. Parameter analysis visualization
    visualize_parameter_analysis(series_dict, parameter_suggestions)

    # 2. Model comparison table
    create_model_comparison_table(recommended_models, parameter_suggestions)

    # 3. Series comparison visualization
    visualize_series_comparison(series_dict)

    print(f"\n=== Phase 2 Complete ===")
    print("✅ Data preparation and EDA completed")
    print("✅ Stationarity testing completed")
    print("✅ ACF/PACF analysis completed")
    print("✅ ARIMA parameter suggestions generated")
    print("✅ Comprehensive visualizations created")
    print("\nReady for Phase 3: Model training and evaluation!")

    return {
        'data_splits': (train_data, test_data, val_data),
        'exog_splits': (train_exog, test_exog, val_exog),
        'series_dict': series_dict,
        'decomposition': decomposition,
        'parameter_suggestions': parameter_suggestions,
        'recommended_models': recommended_models
    }

if __name__ == "__main__":
    results = main()
