"""
Electric Demand Forecasting using ARIMA and Holt-Winter Methods
3-Phase Methodology Implementation

Phase 1: Data Preparation & EDA
Phase 2: Initial Training & Evaluation  
Phase 3: Final Validation & Conclusion
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Statistical and Time Series Libraries
from statsmodels.tsa.arima.model import ARIMA
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.graphics.tsaplots import plot_acf, plot_pacf
from pmdarima import auto_arima

# Metrics
from sklearn.metrics import mean_absolute_error, mean_squared_error, mean_absolute_percentage_error
from sklearn.preprocessing import LabelEncoder

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

class ElectricDemandForecaster:
    def __init__(self, data_path):
        """Initialize the forecaster with data path"""
        self.data_path = data_path
        self.data = None
        self.train_data = None
        self.test_data = None
        self.validation_data = None
        self.sarimax_model = None
        self.holt_winter_model = None
        self.results = {}
        
    def load_and_prepare_data(self):
        """Phase 1: Load and prepare data with datetime indexing and feature engineering"""
        print("="*60)
        print("PHASE 1: DATA PREPARATION & EXPLORATORY DATA ANALYSIS")
        print("="*60)
        
        # Load data
        print("Loading electric demand data...")
        self.data = pd.read_csv(self.data_path)
        print(f"Data shape: {self.data.shape}")
        print(f"Date range: {self.data['DateTime'].min()} to {self.data['DateTime'].max()}")
        
        # Convert DateTime to proper datetime format and set as index
        self.data['DateTime'] = pd.to_datetime(self.data['DateTime'])
        self.data = self.data.set_index('DateTime').sort_index()
        
        # Handle exogenous variables for SARIMAX
        print("\nPreparing exogenous variables...")
        
        # One-hot encode WeekDay (1=Monday, 7=Sunday)
        weekday_dummies = pd.get_dummies(self.data['WeekDay'], prefix='WeekDay')
        self.data = pd.concat([self.data, weekday_dummies], axis=1)
        
        # Keep original variables and create exogenous matrix
        exog_cols = ['Temperature', 'Holiday'] + [col for col in weekday_dummies.columns]
        self.exog_data = self.data[exog_cols].copy()
        
        print(f"Exogenous variables: {exog_cols}")
        print(f"Holiday distribution: {self.data['Holiday'].value_counts().to_dict()}")
        
        return self.data
    
    def exploratory_data_analysis(self):
        """Perform comprehensive EDA"""
        print("\n" + "-"*50)
        print("EXPLORATORY DATA ANALYSIS")
        print("-"*50)
        
        # Basic statistics
        print("\nDemand Statistics:")
        print(self.data['Demand'].describe())
        
        # Time series visualization
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # Full time series
        axes[0,0].plot(self.data.index, self.data['Demand'], alpha=0.7)
        axes[0,0].set_title('Electric Demand Over Time')
        axes[0,0].set_ylabel('Demand')
        
        # Monthly pattern
        monthly_avg = self.data.groupby(self.data.index.month)['Demand'].mean()
        axes[0,1].bar(monthly_avg.index, monthly_avg.values)
        axes[0,1].set_title('Average Demand by Month')
        axes[0,1].set_xlabel('Month')
        axes[0,1].set_ylabel('Average Demand')
        
        # Daily pattern
        hourly_avg = self.data.groupby(self.data.index.hour)['Demand'].mean()
        axes[1,0].plot(hourly_avg.index, hourly_avg.values, marker='o')
        axes[1,0].set_title('Average Demand by Hour of Day')
        axes[1,0].set_xlabel('Hour')
        axes[1,0].set_ylabel('Average Demand')
        
        # Weekly pattern
        weekly_avg = self.data.groupby(self.data['WeekDay'])['Demand'].mean()
        axes[1,1].bar(weekly_avg.index, weekly_avg.values)
        axes[1,1].set_title('Average Demand by Day of Week')
        axes[1,1].set_xlabel('Day of Week (1=Mon, 7=Sun)')
        axes[1,1].set_ylabel('Average Demand')
        
        plt.tight_layout()
        plt.savefig('demand_eda_patterns.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Time series decomposition
        print("\nPerforming time series decomposition...")
        decomposition = seasonal_decompose(self.data['Demand'], model='additive', period=24*7)  # Weekly seasonality
        
        fig, axes = plt.subplots(4, 1, figsize=(15, 12))
        decomposition.observed.plot(ax=axes[0], title='Original')
        decomposition.trend.plot(ax=axes[1], title='Trend')
        decomposition.seasonal.plot(ax=axes[2], title='Seasonal')
        decomposition.resid.plot(ax=axes[3], title='Residual')
        plt.tight_layout()
        plt.savefig('demand_decomposition.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Stationarity test
        print("\nTesting for stationarity (ADF Test):")
        adf_result = adfuller(self.data['Demand'].dropna())
        print(f'ADF Statistic: {adf_result[0]:.6f}')
        print(f'p-value: {adf_result[1]:.6f}')
        print(f'Critical Values:')
        for key, value in adf_result[4].items():
            print(f'\t{key}: {value:.3f}')
        
        if adf_result[1] <= 0.05:
            print("Series is stationary (reject null hypothesis)")
        else:
            print("Series is non-stationary (fail to reject null hypothesis)")
            
        return decomposition
    
    def split_data_chronologically(self):
        """Split data chronologically: 70% train, 20% test, 10% validation"""
        print("\n" + "-"*50)
        print("CHRONOLOGICAL DATA SPLITTING")
        print("-"*50)
        
        total_len = len(self.data)
        train_end = int(0.7 * total_len)
        test_end = int(0.9 * total_len)  # 70% + 20% = 90%
        
        # Split main data
        self.train_data = self.data.iloc[:train_end].copy()
        self.test_data = self.data.iloc[train_end:test_end].copy()
        self.validation_data = self.data.iloc[test_end:].copy()
        
        # Split exogenous data
        self.train_exog = self.exog_data.iloc[:train_end].copy()
        self.test_exog = self.exog_data.iloc[train_end:test_end].copy()
        self.validation_exog = self.exog_data.iloc[test_end:].copy()
        
        print(f"Training set: {len(self.train_data)} samples ({len(self.train_data)/total_len*100:.1f}%)")
        print(f"  Period: {self.train_data.index.min()} to {self.train_data.index.max()}")
        print(f"Test set: {len(self.test_data)} samples ({len(self.test_data)/total_len*100:.1f}%)")
        print(f"  Period: {self.test_data.index.min()} to {self.test_data.index.max()}")
        print(f"Validation set: {len(self.validation_data)} samples ({len(self.validation_data)/total_len*100:.1f}%)")
        print(f"  Period: {self.validation_data.index.min()} to {self.validation_data.index.max()}")
        
        return self.train_data, self.test_data, self.validation_data

    def train_initial_models(self):
        """Phase 2: Train SARIMAX and Holt-Winter models on training data"""
        print("\n" + "="*60)
        print("PHASE 2: INITIAL TRAINING & EVALUATION")
        print("="*60)

        print("Training SARIMAX model...")
        # SARIMAX with seasonal parameters (24 hours, 168 hours for weekly)
        try:
            self.sarimax_model = SARIMAX(
                self.train_data['Demand'],
                exog=self.train_exog,
                order=(1, 1, 1),  # ARIMA order
                seasonal_order=(1, 1, 1, 24),  # Seasonal order with 24-hour period
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            self.sarimax_fitted = self.sarimax_model.fit(disp=False)
            print("SARIMAX model trained successfully")
            print(f"AIC: {self.sarimax_fitted.aic:.2f}")
            print(f"BIC: {self.sarimax_fitted.bic:.2f}")
        except Exception as e:
            print(f"SARIMAX training failed: {e}")
            # Fallback to simpler ARIMA
            print("Falling back to ARIMA model...")
            self.sarimax_model = ARIMA(self.train_data['Demand'], order=(1, 1, 1))
            self.sarimax_fitted = self.sarimax_model.fit()

        print("\nTraining Holt-Winter model...")
        # Holt-Winter Exponential Smoothing
        try:
            self.holt_winter_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24  # Daily seasonality
            )
            self.holt_winter_fitted = self.holt_winter_model.fit()
            print("Holt-Winter model trained successfully")
        except Exception as e:
            print(f"Holt-Winter training failed: {e}")
            # Fallback to simpler version
            self.holt_winter_model = ExponentialSmoothing(
                self.train_data['Demand'],
                trend='add',
                seasonal=None
            )
            self.holt_winter_fitted = self.holt_winter_model.fit()

    def model_diagnostics(self):
        """Perform model diagnostics"""
        print("\n" + "-"*50)
        print("MODEL DIAGNOSTICS")
        print("-"*50)

        # SARIMAX diagnostics
        if hasattr(self.sarimax_fitted, 'resid'):
            print("SARIMAX Model Diagnostics:")
            residuals = self.sarimax_fitted.resid

            # Ljung-Box test for residual autocorrelation
            lb_test = acorr_ljungbox(residuals.dropna(), lags=10, return_df=True)
            print(f"Ljung-Box test p-value: {lb_test['lb_pvalue'].iloc[-1]:.4f}")

            if lb_test['lb_pvalue'].iloc[-1] > 0.05:
                print("Residuals appear to be white noise (good)")
            else:
                print("Residuals show autocorrelation (may need model adjustment)")

            # Plot residuals
            fig, axes = plt.subplots(2, 2, figsize=(12, 8))

            # Residuals over time
            axes[0,0].plot(residuals)
            axes[0,0].set_title('SARIMAX Residuals')
            axes[0,0].set_ylabel('Residuals')

            # Residuals histogram
            axes[0,1].hist(residuals.dropna(), bins=30, alpha=0.7)
            axes[0,1].set_title('Residuals Distribution')
            axes[0,1].set_xlabel('Residuals')

            # ACF of residuals
            plot_acf(residuals.dropna(), ax=axes[1,0], lags=40)
            axes[1,0].set_title('ACF of Residuals')

            # PACF of residuals
            plot_pacf(residuals.dropna(), ax=axes[1,1], lags=40)
            axes[1,1].set_title('PACF of Residuals')

            plt.tight_layout()
            plt.savefig('sarimax_diagnostics.png', dpi=300, bbox_inches='tight')
            plt.show()

    def evaluate_on_test_set(self):
        """Evaluate models on test set"""
        print("\n" + "-"*50)
        print("EVALUATION ON TEST SET")
        print("-"*50)

        test_steps = len(self.test_data)

        # SARIMAX predictions
        print("Generating SARIMAX forecasts...")
        try:
            if hasattr(self.sarimax_fitted, 'forecast'):
                sarimax_forecast = self.sarimax_fitted.forecast(
                    steps=test_steps,
                    exog=self.test_exog if hasattr(self, 'test_exog') else None
                )
            else:
                sarimax_forecast = self.sarimax_fitted.predict(
                    start=len(self.train_data),
                    end=len(self.train_data) + test_steps - 1
                )
        except Exception as e:
            print(f"SARIMAX forecast error: {e}")
            sarimax_forecast = pd.Series([self.train_data['Demand'].mean()] * test_steps,
                                       index=self.test_data.index)

        # Holt-Winter predictions
        print("Generating Holt-Winter forecasts...")
        try:
            holt_winter_forecast = self.holt_winter_fitted.forecast(steps=test_steps)
            holt_winter_forecast.index = self.test_data.index
        except Exception as e:
            print(f"Holt-Winter forecast error: {e}")
            holt_winter_forecast = pd.Series([self.train_data['Demand'].mean()] * test_steps,
                                           index=self.test_data.index)

        # Calculate metrics
        actual_test = self.test_data['Demand']

        # SARIMAX metrics
        sarimax_mae = mean_absolute_error(actual_test, sarimax_forecast)
        sarimax_rmse = np.sqrt(mean_squared_error(actual_test, sarimax_forecast))
        sarimax_mape = np.mean(np.abs((actual_test - sarimax_forecast) / actual_test)) * 100

        # Holt-Winter metrics
        hw_mae = mean_absolute_error(actual_test, holt_winter_forecast)
        hw_rmse = np.sqrt(mean_squared_error(actual_test, holt_winter_forecast))
        hw_mape = np.mean(np.abs((actual_test - holt_winter_forecast) / actual_test)) * 100

        # Store results
        self.results['test_metrics'] = {
            'SARIMAX': {'MAE': sarimax_mae, 'RMSE': sarimax_rmse, 'MAPE': sarimax_mape},
            'Holt-Winter': {'MAE': hw_mae, 'RMSE': hw_rmse, 'MAPE': hw_mape}
        }

        print("\nTest Set Performance:")
        print(f"SARIMAX  - MAE: {sarimax_mae:.2f}, RMSE: {sarimax_rmse:.2f}, MAPE: {sarimax_mape:.2f}%")
        print(f"Holt-Winter - MAE: {hw_mae:.2f}, RMSE: {hw_rmse:.2f}, MAPE: {hw_mape:.2f}%")

        # Visualization
        plt.figure(figsize=(15, 8))
        plt.plot(actual_test.index, actual_test.values, label='Actual', linewidth=2)
        plt.plot(sarimax_forecast.index, sarimax_forecast.values, label='SARIMAX', alpha=0.8)
        plt.plot(holt_winter_forecast.index, holt_winter_forecast.values, label='Holt-Winter', alpha=0.8)
        plt.title('Test Set Forecasting Results')
        plt.xlabel('Date')
        plt.ylabel('Electric Demand')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('test_set_forecasts.png', dpi=300, bbox_inches='tight')
        plt.show()

        return sarimax_forecast, holt_winter_forecast

    def retrain_on_combined_data(self):
        """Phase 3: Retrain models on combined train+test data"""
        print("\n" + "="*60)
        print("PHASE 3: FINAL VALIDATION & CONCLUSION")
        print("="*60)

        print("Retraining models on combined train+test data...")

        # Combine train and test data
        combined_data = pd.concat([self.train_data, self.test_data])
        combined_exog = pd.concat([self.train_exog, self.test_exog])

        print(f"Combined training data: {len(combined_data)} samples")
        print(f"Period: {combined_data.index.min()} to {combined_data.index.max()}")

        # Retrain SARIMAX
        print("\nRetraining SARIMAX model...")
        try:
            self.sarimax_final = SARIMAX(
                combined_data['Demand'],
                exog=combined_exog,
                order=(1, 1, 1),
                seasonal_order=(1, 1, 1, 24),
                enforce_stationarity=False,
                enforce_invertibility=False
            )
            self.sarimax_final_fitted = self.sarimax_final.fit(disp=False)
            print("SARIMAX retrained successfully")
        except Exception as e:
            print(f"SARIMAX retraining failed: {e}")
            self.sarimax_final = ARIMA(combined_data['Demand'], order=(1, 1, 1))
            self.sarimax_final_fitted = self.sarimax_final.fit()

        # Retrain Holt-Winter
        print("Retraining Holt-Winter model...")
        try:
            self.holt_winter_final = ExponentialSmoothing(
                combined_data['Demand'],
                trend='add',
                seasonal='add',
                seasonal_periods=24
            )
            self.holt_winter_final_fitted = self.holt_winter_final.fit()
            print("Holt-Winter retrained successfully")
        except Exception as e:
            print(f"Holt-Winter retraining failed: {e}")
            self.holt_winter_final = ExponentialSmoothing(
                combined_data['Demand'],
                trend='add',
                seasonal=None
            )
            self.holt_winter_final_fitted = self.holt_winter_final.fit()

    def final_validation_forecast(self):
        """Generate final forecasts on validation set with multiple horizons"""
        print("\n" + "-"*50)
        print("FINAL VALIDATION FORECASTING")
        print("-"*50)

        validation_steps = len(self.validation_data)

        # Generate forecasts for different horizons
        horizons = {
            '24h': min(24, validation_steps),
            '168h': min(168, validation_steps),  # 1 week
            'full': validation_steps
        }

        final_results = {}

        for horizon_name, steps in horizons.items():
            print(f"\nForecasting {horizon_name} ({steps} steps)...")

            # SARIMAX forecast
            try:
                if hasattr(self.sarimax_final_fitted, 'forecast'):
                    sarimax_final_forecast = self.sarimax_final_fitted.forecast(
                        steps=steps,
                        exog=self.validation_exog.iloc[:steps] if hasattr(self, 'validation_exog') else None
                    )
                else:
                    start_idx = len(self.train_data) + len(self.test_data)
                    sarimax_final_forecast = self.sarimax_final_fitted.predict(
                        start=start_idx,
                        end=start_idx + steps - 1
                    )
                sarimax_final_forecast.index = self.validation_data.index[:steps]
            except Exception as e:
                print(f"SARIMAX final forecast error: {e}")
                sarimax_final_forecast = pd.Series(
                    [self.train_data['Demand'].mean()] * steps,
                    index=self.validation_data.index[:steps]
                )

            # Holt-Winter forecast
            try:
                holt_winter_final_forecast = self.holt_winter_final_fitted.forecast(steps=steps)
                holt_winter_final_forecast.index = self.validation_data.index[:steps]
            except Exception as e:
                print(f"Holt-Winter final forecast error: {e}")
                holt_winter_final_forecast = pd.Series(
                    [self.train_data['Demand'].mean()] * steps,
                    index=self.validation_data.index[:steps]
                )

            # Calculate metrics
            actual_validation = self.validation_data['Demand'].iloc[:steps]

            # SARIMAX metrics
            sarimax_mae = mean_absolute_error(actual_validation, sarimax_final_forecast)
            sarimax_rmse = np.sqrt(mean_squared_error(actual_validation, sarimax_final_forecast))
            sarimax_mape = np.mean(np.abs((actual_validation - sarimax_final_forecast) / actual_validation)) * 100

            # Holt-Winter metrics
            hw_mae = mean_absolute_error(actual_validation, holt_winter_final_forecast)
            hw_rmse = np.sqrt(mean_squared_error(actual_validation, holt_winter_final_forecast))
            hw_mape = np.mean(np.abs((actual_validation - holt_winter_final_forecast) / actual_validation)) * 100

            final_results[horizon_name] = {
                'SARIMAX': {'MAE': sarimax_mae, 'RMSE': sarimax_rmse, 'MAPE': sarimax_mape},
                'Holt-Winter': {'MAE': hw_mae, 'RMSE': hw_rmse, 'MAPE': hw_mape},
                'forecasts': {
                    'actual': actual_validation,
                    'sarimax': sarimax_final_forecast,
                    'holt_winter': holt_winter_final_forecast
                }
            }

            print(f"{horizon_name} Performance:")
            print(f"  SARIMAX     - MAE: {sarimax_mae:.2f}, RMSE: {sarimax_rmse:.2f}, MAPE: {sarimax_mape:.2f}%")
            print(f"  Holt-Winter - MAE: {hw_mae:.2f}, RMSE: {hw_rmse:.2f}, MAPE: {hw_mape:.2f}%")

        self.results['validation_metrics'] = final_results
        return final_results

    def final_comparison_and_conclusion(self):
        """Generate final comparison visualizations and conclusions"""
        print("\n" + "-"*50)
        print("FINAL COMPARISON & CONCLUSION")
        print("-"*50)

        # Create comprehensive comparison plots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))

        # Plot 24h forecast
        if '24h' in self.results['validation_metrics']:
            data_24h = self.results['validation_metrics']['24h']['forecasts']
            axes[0,0].plot(data_24h['actual'].index, data_24h['actual'].values,
                          label='Actual', linewidth=2, color='black')
            axes[0,0].plot(data_24h['sarimax'].index, data_24h['sarimax'].values,
                          label='SARIMAX', alpha=0.8)
            axes[0,0].plot(data_24h['holt_winter'].index, data_24h['holt_winter'].values,
                          label='Holt-Winter', alpha=0.8)
            axes[0,0].set_title('24-Hour Forecast vs Actual')
            axes[0,0].legend()
            axes[0,0].grid(True, alpha=0.3)

        # Plot 168h forecast
        if '168h' in self.results['validation_metrics']:
            data_168h = self.results['validation_metrics']['168h']['forecasts']
            axes[0,1].plot(data_168h['actual'].index, data_168h['actual'].values,
                          label='Actual', linewidth=2, color='black')
            axes[0,1].plot(data_168h['sarimax'].index, data_168h['sarimax'].values,
                          label='SARIMAX', alpha=0.8)
            axes[0,1].plot(data_168h['holt_winter'].index, data_168h['holt_winter'].values,
                          label='Holt-Winter', alpha=0.8)
            axes[0,1].set_title('168-Hour (1 Week) Forecast vs Actual')
            axes[0,1].legend()
            axes[0,1].grid(True, alpha=0.3)

        # Performance comparison bar chart
        horizons = list(self.results['validation_metrics'].keys())
        sarimax_maes = [self.results['validation_metrics'][h]['SARIMAX']['MAE'] for h in horizons]
        hw_maes = [self.results['validation_metrics'][h]['Holt-Winter']['MAE'] for h in horizons]

        x = np.arange(len(horizons))
        width = 0.35

        axes[1,0].bar(x - width/2, sarimax_maes, width, label='SARIMAX', alpha=0.8)
        axes[1,0].bar(x + width/2, hw_maes, width, label='Holt-Winter', alpha=0.8)
        axes[1,0].set_xlabel('Forecast Horizon')
        axes[1,0].set_ylabel('Mean Absolute Error (MAE)')
        axes[1,0].set_title('MAE Comparison Across Horizons')
        axes[1,0].set_xticks(x)
        axes[1,0].set_xticklabels(horizons)
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)

        # MAPE comparison
        sarimax_mapes = [self.results['validation_metrics'][h]['SARIMAX']['MAPE'] for h in horizons]
        hw_mapes = [self.results['validation_metrics'][h]['Holt-Winter']['MAPE'] for h in horizons]

        axes[1,1].bar(x - width/2, sarimax_mapes, width, label='SARIMAX', alpha=0.8)
        axes[1,1].bar(x + width/2, hw_mapes, width, label='Holt-Winter', alpha=0.8)
        axes[1,1].set_xlabel('Forecast Horizon')
        axes[1,1].set_ylabel('Mean Absolute Percentage Error (MAPE %)')
        axes[1,1].set_title('MAPE Comparison Across Horizons')
        axes[1,1].set_xticks(x)
        axes[1,1].set_xticklabels(horizons)
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('final_comparison_results.png', dpi=300, bbox_inches='tight')
        plt.show()

        # Print final conclusions
        print("\n" + "="*60)
        print("FINAL CONCLUSIONS")
        print("="*60)

        # Determine best model for each horizon
        for horizon in horizons:
            sarimax_mae = self.results['validation_metrics'][horizon]['SARIMAX']['MAE']
            hw_mae = self.results['validation_metrics'][horizon]['Holt-Winter']['MAE']

            best_model = 'SARIMAX' if sarimax_mae < hw_mae else 'Holt-Winter'
            improvement = abs(sarimax_mae - hw_mae) / max(sarimax_mae, hw_mae) * 100

            print(f"\n{horizon} Forecast Horizon:")
            print(f"  Best Model: {best_model}")
            print(f"  Performance Improvement: {improvement:.1f}%")
            print(f"  SARIMAX MAE: {sarimax_mae:.2f}")
            print(f"  Holt-Winter MAE: {hw_mae:.2f}")

        # Overall recommendation
        avg_sarimax_mae = np.mean([self.results['validation_metrics'][h]['SARIMAX']['MAE'] for h in horizons])
        avg_hw_mae = np.mean([self.results['validation_metrics'][h]['Holt-Winter']['MAE'] for h in horizons])

        overall_best = 'SARIMAX' if avg_sarimax_mae < avg_hw_mae else 'Holt-Winter'

        print(f"\n{'='*60}")
        print(f"OVERALL RECOMMENDATION: {overall_best}")
        print(f"Average MAE - SARIMAX: {avg_sarimax_mae:.2f}, Holt-Winter: {avg_hw_mae:.2f}")
        print(f"{'='*60}")

        return overall_best

    def forecast_validation_periods(self):
        """Forecast 1-day (24h) and 1-week (168h) periods in validation set"""
        print("\n" + "="*60)
        print("VALIDATION SET FORECASTING (1-DAY AND 1-WEEK)")
        print("="*60)
        
        # Ensure models are trained on combined training + testing data
        if not hasattr(self, 'final_sarimax_model'):
            print("Training final models on combined training + testing data...")
            self.retrain_on_combined_data()
        
        # Prepare validation data for both periods
        val_start = self.validation_data.index[0]
        day_end = val_start + pd.Timedelta(hours=23)
        week_end = val_start + pd.Timedelta(hours=167)
        
        # Get actual values for comparison
        actual_day = self.validation_data.loc[:day_end, 'Demand']
        actual_week = self.validation_data.loc[:week_end, 'Demand']
        
        # SARIMAX Forecasts
        print("\nGenerating SARIMAX forecasts...")
        sarimax_day = self.final_sarimax_fitted.forecast(
            steps=24,
            exog=self.validation_exog.loc[:day_end]
        )
        sarimax_week = self.final_sarimax_fitted.forecast(
            steps=168,
            exog=self.validation_exog.loc[:week_end]
        )
        
        # Holt-Winters Forecasts
        print("Generating Holt-Winters forecasts...")
        hw_day = self.final_hw_model.forecast(24)
        hw_week = self.final_hw_model.forecast(168)
        
        # Calculate metrics for both models and periods
        metrics = {}
        for name, pred_day, pred_week in [
            ('SARIMAX', sarimax_day, sarimax_week),
            ('Holt-Winters', hw_day, hw_week)
        ]:
            metrics[name] = {
                '24h': {
                    'RMSE': np.sqrt(mean_squared_error(actual_day, pred_day)),
                    'MAE': mean_absolute_error(actual_day, pred_day),
                    'MAPE': mean_absolute_percentage_error(actual_day, pred_day) * 100
                },
                '168h': {
                    'RMSE': np.sqrt(mean_squared_error(actual_week, pred_week)),
                    'MAE': mean_absolute_error(actual_week, pred_week),
                    'MAPE': mean_absolute_percentage_error(actual_week, pred_week) * 100
                }
            }
        
        # Print metrics
        print("\nValidation Set Metrics:")
        for model in metrics:
            print(f"\n{model} Results:")
            print("24-hour Forecast:")
            print(f"  RMSE: {metrics[model]['24h']['RMSE']:.2f}")
            print(f"  MAE:  {metrics[model]['24h']['MAE']:.2f}")
            print(f"  MAPE: {metrics[model]['24h']['MAPE']:.2f}%")
            print("168-hour Forecast:")
            print(f"  RMSE: {metrics[model]['168h']['RMSE']:.2f}")
            print(f"  MAE:  {metrics[model]['168h']['MAE']:.2f}")
            print(f"  MAPE: {metrics[model]['168h']['MAPE']:.2f}%")
        
        # Visualize forecasts
        plt.figure(figsize=(15, 10))
        
        # Plot 24-hour forecasts
        plt.subplot(2, 1, 1)
        actual_day.plot(label='Actual', alpha=0.7)
        sarimax_day.plot(label='SARIMAX', linestyle='--')
        hw_day.plot(label='Holt-Winters', linestyle=':')
        plt.title('24-Hour Forecast Comparison')
        plt.legend()
        plt.grid(True)
        
        # Plot 168-hour forecasts
        plt.subplot(2, 1, 2)
        actual_week.plot(label='Actual', alpha=0.7)
        sarimax_week.plot(label='SARIMAX', linestyle='--')
        hw_week.plot(label='Holt-Winters', linestyle=':')
        plt.title('168-Hour Forecast Comparison')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        plt.savefig('validation_forecasts.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return metrics

    def run_complete_analysis(self):
        """Run the complete forecasting analysis pipeline"""
        # Phase 1: Data Preparation & EDA
        self.load_and_prepare_data()
        self.exploratory_data_analysis()
        
        # Phase 2: Initial Training & Model Selection
        self.split_data_chronologically()
        self.train_initial_models()
        self.model_diagnostics()
        self.evaluate_on_test_set()
        
        # Phase 3: Final Validation
        self.retrain_on_combined_data()
        validation_metrics = self.forecast_validation_periods()
        self.final_comparison_and_conclusion()
        
        return validation_metrics

def main():
    """Main execution function"""
    # Initialize forecaster
    forecaster = ElectricDemandForecaster('electric_demand_1h.csv')
    
    # Run complete analysis
    metrics = forecaster.run_complete_analysis()
    
    # Save results summary
    with open('forecasting_results_summary.md', 'w') as f:
        f.write("# Electric Demand Forecasting Results Summary\n\n")
        f.write("## Validation Set Performance\n\n")
        for model in metrics:
            f.write(f"### {model}\n\n")
            f.write("#### 24-hour Forecast\n")
            f.write(f"- RMSE: {metrics[model]['24h']['RMSE']:.2f}\n")
            f.write(f"- MAE:  {metrics[model]['24h']['MAE']:.2f}\n")
            f.write(f"- MAPE: {metrics[model]['24h']['MAPE']:.2f}%\n\n")
            f.write("#### 168-hour Forecast\n")
            f.write(f"- RMSE: {metrics[model]['168h']['RMSE']:.2f}\n")
            f.write(f"- MAE:  {metrics[model]['168h']['MAE']:.2f}\n")
            f.write(f"- MAPE: {metrics[model]['168h']['MAPE']:.2f}%\n\n")

if __name__ == "__main__":
    main()
